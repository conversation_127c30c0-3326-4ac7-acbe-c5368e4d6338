import sequelizePkg from 'sequelize'
import sequelize from '../lib/sequelize.js'

const { DataTypes } = sequelizePkg

const UserAchievement = sequelize.define('UserAchievement', {
  id: {
    type: DataTypes.BIGINT,
    primaryKey: true,
    autoIncrement: true
  },
  user_id: {
    type: DataTypes.BIGINT,
    allowNull: false,
    comment: '用户ID'
  },
  achievement_id: {
    type: DataTypes.BIGINT,
    allowNull: false,
    comment: '成就ID'
  },
  unlocked_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW,
    comment: '解锁时间'
  },
  progress: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    comment: '当前进度'
  },
  is_claimed: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: '是否已领取奖励'
  },
  claimed_at: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '领取时间'
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW,
    comment: '创建时间'
  }
}, {
  tableName: 'user_achievements',
  timestamps: false,
  comment: '用户成就记录表',
  indexes: [
    {
      unique: true,
      fields: ['user_id', 'achievement_id']
    }
  ]
})

export default UserAchievement
