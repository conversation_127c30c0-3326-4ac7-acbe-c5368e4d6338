### 管理员管理接口测试
### 引用全局变量
< ../common/variables.http

### 前置条件：需要先获取管理员token
< ../auth/admin-auth.http

### 1. 获取管理员列表 - 默认分页
GET {{baseUrl}}/admin/admins
Authorization: Bearer {{adminToken}}

### 2. 获取管理员列表 - 指定分页
GET {{baseUrl}}/admin/admins?page={{defaultPage}}&limit={{defaultLimit}}
Authorization: Bearer {{adminToken}}

### 3. 获取管理员列表 - 大分页
GET {{baseUrl}}/admin/admins?page=1&limit={{maxLimit}}
Authorization: Bearer {{adminToken}}

### 4. 获取管理员列表 - 搜索功能
GET {{baseUrl}}/admin/admins?search=admin&page=1&limit=10
Authorization: Bearer {{adminToken}}

### 5. 获取管理员列表 - 按角色筛选
GET {{baseUrl}}/admin/admins?role=super_admin&page=1&limit=10
Authorization: Bearer {{adminToken}}

### 6. 获取管理员列表 - 按状态筛选
GET {{baseUrl}}/admin/admins?status=active&page=1&limit=10
Authorization: Bearer {{adminToken}}

### 7. 创建新管理员 - 普通管理员
POST {{baseUrl}}/admin/admins
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "username": "test_admin",
  "password": "test123456",
  "email": "<EMAIL>",
  "role": "admin",
  "real_name": "测试管理员",
  "phone": "13800138000",
  "status": "active"
}

### 8. 创建新管理员 - 超级管理员
POST {{baseUrl}}/admin/admins
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "username": "test_super_admin",
  "password": "super123456",
  "email": "<EMAIL>",
  "role": "super_admin",
  "real_name": "测试超级管理员",
  "phone": "13900139000",
  "status": "active"
}

### 9. 创建新管理员 - 缺少必填字段
POST {{baseUrl}}/admin/admins
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "email": "<EMAIL>",
  "role": "admin"
}

### 10. 创建新管理员 - 用户名重复
POST {{baseUrl}}/admin/admins
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "username": "admin",
  "password": "duplicate123",
  "email": "<EMAIL>",
  "role": "admin",
  "real_name": "重复用户名测试"
}

### 11. 创建新管理员 - 邮箱重复
POST {{baseUrl}}/admin/admins
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "username": "duplicate_email_admin",
  "password": "duplicate123",
  "email": "<EMAIL>",
  "role": "admin",
  "real_name": "重复邮箱测试"
}

### 12. 创建新管理员 - 无效邮箱格式
POST {{baseUrl}}/admin/admins
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "username": "invalid_email_admin",
  "password": "test123456",
  "email": "invalid_email",
  "role": "admin",
  "real_name": "无效邮箱测试"
}

### 13. 创建新管理员 - 弱密码
POST {{baseUrl}}/admin/admins
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "username": "weak_password_admin",
  "password": "123",
  "email": "<EMAIL>",
  "role": "admin",
  "real_name": "弱密码测试"
}

### 14. 更新管理员信息 - 正常更新
PUT {{baseUrl}}/admin/admins/2
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "email": "<EMAIL>",
  "real_name": "更新后的管理员",
  "phone": "13700137000",
  "status": "active"
}

### 15. 更新管理员信息 - 更新角色
PUT {{baseUrl}}/admin/admins/2
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "role": "super_admin"
}

### 16. 更新管理员信息 - 禁用管理员
PUT {{baseUrl}}/admin/admins/2
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "status": "inactive"
}

### 17. 更新管理员信息 - 不存在的管理员
PUT {{baseUrl}}/admin/admins/99999
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "real_name": "不存在的管理员"
}

### 18. 更新管理员信息 - 无效的角色
PUT {{baseUrl}}/admin/admins/2
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "role": "invalid_role"
}

### 19. 删除管理员 - 正常删除
DELETE {{baseUrl}}/admin/admins/3
Authorization: Bearer {{adminToken}}

### 20. 删除管理员 - 删除不存在的管理员
DELETE {{baseUrl}}/admin/admins/99999
Authorization: Bearer {{adminToken}}

### 21. 删除管理员 - 尝试删除自己
DELETE {{baseUrl}}/admin/admins/1
Authorization: Bearer {{adminToken}}

### 22. 权限测试 - 普通管理员尝试管理其他管理员
# 注意：需要先创建一个普通管理员账户并获取其token
GET {{baseUrl}}/admin/admins
Authorization: Bearer normal_admin_token_here

### 23. 权限测试 - 无token访问
GET {{baseUrl}}/admin/admins

### 24. 权限测试 - 无效token访问
GET {{baseUrl}}/admin/admins
Authorization: Bearer invalid_token

### 25. 批量操作测试 - 连续创建多个管理员
POST {{baseUrl}}/admin/admins
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "username": "batch_admin_1",
  "password": "batch123456",
  "email": "<EMAIL>",
  "role": "admin",
  "real_name": "批量管理员1"
}

###
POST {{baseUrl}}/admin/admins
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "username": "batch_admin_2",
  "password": "batch123456",
  "email": "<EMAIL>",
  "role": "admin",
  "real_name": "批量管理员2"
}
