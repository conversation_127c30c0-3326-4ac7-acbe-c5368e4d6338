import sequelizePkg from 'sequelize'
import sequelize from '../lib/sequelize.js'

const { DataTypes } = sequelizePkg

const EnergyRewardRule = sequelize.define('EnergyRewardRule', {
  id: {
    type: DataTypes.BIGINT,
    primaryKey: true,
    autoIncrement: true
  },
  rule_name: {
    type: DataTypes.STRING(128),
    allowNull: false,
    comment: '规则名称'
  },
  rule_type: {
    type: DataTypes.ENUM('meditation_complete', 'daily_streak', 'level_up', 'special_event'),
    allowNull: false,
    comment: '规则类型：完成冥想/连续天数/升级/特殊事件'
  },
  condition: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '触发条件（JSON格式）'
  },
  energy_amount: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: '奖励能量值'
  },
  bonus_multiplier: {
    type: DataTypes.DECIMAL(3, 2),
    defaultValue: 1.0,
    comment: '奖励倍数'
  },
  max_daily_times: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    comment: '每日最大触发次数（0为无限制）'
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '规则描述'
  },
  is_active: {
    type: DataTypes.BOOLEAN,
    defaultValue: true,
    comment: '是否启用'
  },
  priority: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    comment: '优先级（数字越大优先级越高）'
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW,
    comment: '创建时间'
  },
  updated_at: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '更新时间'
  }
}, {
  tableName: 'energy_reward_rules',
  timestamps: false,
  comment: '能量奖励规则表'
})

export default EnergyRewardRule
