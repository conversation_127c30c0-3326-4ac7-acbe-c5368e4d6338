import User from './User.js'
import MeditationContent from './MeditationContent.js'
import UserFavorite from './UserFavorite.js'
import UserMeditationStats from './UserMeditationStats.js'
import Plan from './Plan.js'
import PlanItem from './PlanItem.js'
import PlanHistory from './PlanHistory.js'
import Plant from './Plant.js'
import PlantGrowthRecord from './PlantGrowthRecord.js'
import PlantLevelConfig from './PlantLevelConfig.js'
import MeditationTag from './MeditationTag.js'
import MeditationContentTag from './MeditationContentTag.js'
import Achievement from './Achievement.js'
import UserAchievement from './UserAchievement.js'

// 用户与收藏关系
User.hasMany(UserFavorite, { foreignKey: 'user_id', as: 'favorites' })
UserFavorite.belongsTo(User, { foreignKey: 'user_id', as: 'user' })

MeditationContent.hasMany(UserFavorite, { foreignKey: 'meditation_id', as: 'favorites' })
UserFavorite.belongsTo(MeditationContent, { foreignKey: 'meditation_id', as: 'meditation' })

// 用户与冥想统计关系
User.hasMany(UserMeditationStats, { foreignKey: 'user_id', as: 'meditation_stats' })
UserMeditationStats.belongsTo(User, { foreignKey: 'user_id', as: 'user' })

// 冥想内容父子关系
MeditationContent.hasMany(MeditationContent, { foreignKey: 'parent_id', as: 'children' })
MeditationContent.belongsTo(MeditationContent, { foreignKey: 'parent_id', as: 'parent' })

// 用户与计划关系
User.hasMany(Plan, { foreignKey: 'user_id', as: 'plans' })
Plan.belongsTo(User, { foreignKey: 'user_id', as: 'user' })

// 计划与计划项关系
Plan.hasMany(PlanItem, { foreignKey: 'plan_id', as: 'items' })
PlanItem.belongsTo(Plan, { foreignKey: 'plan_id', as: 'plan' })

// 计划项与冥想内容关系
MeditationContent.hasMany(PlanItem, { foreignKey: 'meditation_id', as: 'plan_items' })
PlanItem.belongsTo(MeditationContent, { foreignKey: 'meditation_id', as: 'meditation' })

// 用户与计划历史关系
User.hasMany(PlanHistory, { foreignKey: 'user_id', as: 'plan_history' })
PlanHistory.belongsTo(User, { foreignKey: 'user_id', as: 'user' })

MeditationContent.hasMany(PlanHistory, { foreignKey: 'meditation_id', as: 'plan_history' })
PlanHistory.belongsTo(MeditationContent, { foreignKey: 'meditation_id', as: 'meditation' })

// 用户与多肉关系
User.hasMany(Plant, { foreignKey: 'user_id', as: 'plants' })
Plant.belongsTo(User, { foreignKey: 'user_id', as: 'user' })

// 多肉与成长记录关系
Plant.hasMany(PlantGrowthRecord, { foreignKey: 'plant_id', as: 'growth_records' })
PlantGrowthRecord.belongsTo(Plant, { foreignKey: 'plant_id', as: 'plant' })

// 冥想内容与标签关系
MeditationContent.belongsToMany(MeditationTag, { 
  through: MeditationContentTag, 
  foreignKey: 'meditation_id',
  otherKey: 'tag_id',
  as: 'tags'
})

MeditationTag.belongsToMany(MeditationContent, {
  through: MeditationContentTag,
  foreignKey: 'tag_id',
  otherKey: 'meditation_id',
  as: 'meditations'
})

// 用户与成就关系
User.belongsToMany(Achievement, {
  through: UserAchievement,
  foreignKey: 'user_id',
  otherKey: 'achievement_id',
  as: 'achievements'
})

Achievement.belongsToMany(User, {
  through: UserAchievement,
  foreignKey: 'achievement_id',
  otherKey: 'user_id',
  as: 'users'
})

// 用户成就记录关系
User.hasMany(UserAchievement, { foreignKey: 'user_id', as: 'user_achievements' })
UserAchievement.belongsTo(User, { foreignKey: 'user_id', as: 'user' })

Achievement.hasMany(UserAchievement, { foreignKey: 'achievement_id', as: 'user_achievements' })
UserAchievement.belongsTo(Achievement, { foreignKey: 'achievement_id', as: 'achievement' })

export {
  User,
  MeditationContent,
  UserFavorite,
  UserMeditationStats,
  Plan,
  PlanItem,
  PlanHistory,
  Plant,
  PlantGrowthRecord,
  PlantLevelConfig,
  MeditationTag,
  MeditationContentTag,
  Achievement,
  UserAchievement
}
