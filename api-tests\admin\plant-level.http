### 多肉等级管理接口测试
### 引用全局变量
< ../common/variables.http

### 前置条件：需要先获取管理员token
< ../auth/admin-auth.http

### 1. 获取多肉等级配置列表 - 默认分页
GET {{baseUrl}}/admin/plants/levels?page={{defaultPage}}&limit={{defaultLimit}}
Authorization: Bearer {{adminToken}}

### 2. 获取多肉等级配置列表 - 搜索功能
GET {{baseUrl}}/admin/plants/levels?page=1&limit=10&search=幼苗
Authorization: Bearer {{adminToken}}

### 3. 获取多肉等级配置列表 - 按状态筛选（启用）
GET {{baseUrl}}/admin/plants/levels?page=1&limit=10&is_active=true
Authorization: Bearer {{adminToken}}

### 4. 获取多肉等级配置列表 - 按状态筛选（禁用）
GET {{baseUrl}}/admin/plants/levels?page=1&limit=10&is_active=false
Authorization: Bearer {{adminToken}}

### 5. 获取多肉等级配置列表 - 按等级排序
GET {{baseUrl}}/admin/plants/levels?page=1&limit=10&sort=level&order=asc
Authorization: Bearer {{adminToken}}

### 6. 获取多肉等级配置列表 - 按能量需求排序
GET {{baseUrl}}/admin/plants/levels?page=1&limit=10&sort=required_energy&order=desc
Authorization: Bearer {{adminToken}}

### 7. 获取指定等级配置详情 - 正常等级
GET {{baseUrl}}/admin/plants/levels/{{testLevelId}}
Authorization: Bearer {{adminToken}}

### 8. 获取指定等级配置详情 - 不存在的等级
GET {{baseUrl}}/admin/plants/levels/99999
Authorization: Bearer {{adminToken}}

### 9. 创建新的等级配置 - 完整配置
POST {{baseUrl}}/admin/plants/levels
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "level": 6,
  "name": "神话",
  "icon": "/images/levels/mythical.png",
  "required_energy": 2500,
  "required_days": 60,
  "attribute_bonus": {
    "growth_speed": 5.0,
    "energy_efficiency": 3.0,
    "resistance": 3.0,
    "beauty": 4.0,
    "magic": 2.0
  },
  "special_ability": {
    "abilities": ["神话光合作用", "时空适应", "瞬间重生", "环境改造", "灵气爆发", "生命赐予"]
  },
  "unlock_reward": {
    "coins": 500,
    "experience": 200,
    "items": ["fertilizer_mythical", "decoration_mythical", "seed_mythical"],
    "special_title": "多肉之神"
  },
  "description": "超越传说的神话级多肉，拥有改变世界的神奇力量",
  "sort_order": 6
}

### 10. 创建新的等级配置 - 最小配置
POST {{baseUrl}}/admin/plants/levels
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "level": 7,
  "name": "测试等级",
  "required_energy": 100,
  "required_days": 10,
  "description": "测试用等级配置"
}

### 11. 创建新的等级配置 - 缺少必填字段
POST {{baseUrl}}/admin/plants/levels
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "name": "缺少等级字段",
  "required_energy": 100
}

### 12. 创建新的等级配置 - 重复等级
POST {{baseUrl}}/admin/plants/levels
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "level": 1,
  "name": "重复等级测试",
  "required_energy": 50,
  "required_days": 5,
  "description": "测试重复等级"
}

### 13. 创建新的等级配置 - 负数等级
POST {{baseUrl}}/admin/plants/levels
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "level": -1,
  "name": "负数等级",
  "required_energy": 100,
  "required_days": 10,
  "description": "测试负数等级"
}

### 14. 创建新的等级配置 - 零能量需求
POST {{baseUrl}}/admin/plants/levels
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "level": 8,
  "name": "零能量等级",
  "required_energy": 0,
  "required_days": 1,
  "description": "测试零能量需求"
}

### 15. 更新等级配置 - 正常更新
PUT {{baseUrl}}/admin/plants/levels/{{testLevelId}}
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "name": "幼苗（更新）",
  "description": "刚刚发芽的小苗，充满生机与希望（已更新）",
  "required_energy": 15
}

### 16. 更新等级配置 - 更新属性加成
PUT {{baseUrl}}/admin/plants/levels/{{testLevelId}}
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "attribute_bonus": {
    "growth_speed": 1.5,
    "energy_efficiency": 1.2,
    "resistance": 1.1,
    "beauty": 1.3
  }
}

### 17. 更新等级配置 - 更新特殊能力
PUT {{baseUrl}}/admin/plants/levels/{{testLevelId}}
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "special_ability": {
    "abilities": ["基础光合作用", "水分吸收", "养分转化"]
  }
}

### 18. 更新等级配置 - 更新解锁奖励
PUT {{baseUrl}}/admin/plants/levels/{{testLevelId}}
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "unlock_reward": {
    "coins": 50,
    "experience": 20,
    "items": ["fertilizer_basic", "decoration_basic"],
    "special_title": "多肉新手"
  }
}

### 19. 更新等级配置 - 不存在的等级
PUT {{baseUrl}}/admin/plants/levels/99999
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "name": "不存在的等级"
}

### 20. 切换等级配置状态 - 禁用等级
PUT {{baseUrl}}/admin/plants/levels/{{testLevelId}}/toggle
Authorization: Bearer {{adminToken}}

### 21. 切换等级配置状态 - 启用等级
PUT {{baseUrl}}/admin/plants/levels/{{testLevelId}}/toggle
Authorization: Bearer {{adminToken}}

### 22. 切换等级配置状态 - 不存在的等级
PUT {{baseUrl}}/admin/plants/levels/99999/toggle
Authorization: Bearer {{adminToken}}

### 23. 删除等级配置 - 正常删除
DELETE {{baseUrl}}/admin/plants/levels/6
Authorization: Bearer {{adminToken}}

### 24. 删除等级配置 - 不存在的等级
DELETE {{baseUrl}}/admin/plants/levels/99999
Authorization: Bearer {{adminToken}}

### 25. 删除等级配置 - 尝试删除正在使用的等级
DELETE {{baseUrl}}/admin/plants/levels/1
Authorization: Bearer {{adminToken}}

### 26. 获取所有启用的等级配置 - 用于前端下拉选择
GET {{baseUrl}}/admin/plants/levels?is_active=true&limit=100
Authorization: Bearer {{adminToken}}

### 27. 批量操作测试 - 连续创建多个等级
POST {{baseUrl}}/admin/plants/levels
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "level": 9,
  "name": "批量测试等级1",
  "required_energy": 300,
  "required_days": 15,
  "description": "批量创建测试1"
}

###
POST {{baseUrl}}/admin/plants/levels
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "level": 10,
  "name": "批量测试等级2",
  "required_energy": 400,
  "required_days": 20,
  "description": "批量创建测试2"
}

### 28. 数据验证测试 - 超长名称
POST {{baseUrl}}/admin/plants/levels
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "level": 11,
  "name": "超长名称测试".repeat(20),
  "required_energy": 100,
  "required_days": 10,
  "description": "测试超长名称"
}

### 29. 数据验证测试 - 特殊字符
POST {{baseUrl}}/admin/plants/levels
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "level": 12,
  "name": "特殊字符@#$%^&*()",
  "required_energy": 100,
  "required_days": 10,
  "description": "测试特殊字符"
}

### 30. 权限测试 - 无token访问
GET {{baseUrl}}/admin/plants/levels

### 31. 权限测试 - 无效token访问
GET {{baseUrl}}/admin/plants/levels
Authorization: Bearer invalid_token

### 32. 性能测试 - 查询大量数据
GET {{baseUrl}}/admin/plants/levels?page=1&limit=100
Authorization: Bearer {{adminToken}}

### 33. 排序测试 - 多字段排序
GET {{baseUrl}}/admin/plants/levels?sort=level,required_energy&order=asc,desc&page=1&limit=10
Authorization: Bearer {{adminToken}}

### 34. 筛选测试 - 能量范围筛选
GET {{baseUrl}}/admin/plants/levels?min_energy=100&max_energy=1000&page=1&limit=10
Authorization: Bearer {{adminToken}}

### 35. 统计测试 - 等级配置统计
GET {{baseUrl}}/admin/plants/levels/statistics
Authorization: Bearer {{adminToken}}
