import Sequelize from 'sequelize'
import sequelize from '../lib/sequelize.js'

const MeditationContent = sequelize.define('meditation_content', {
  id: {
    type: Sequelize.BIGINT,
    primaryKey: true,
    autoIncrement: true
  },
  type: {
    type: Sequelize.ENUM('meditation', 'sleep', 'sound'),
    allowNull: false,
    comment: '类型：冥想/睡眠/声音'
  },
  sub_type: {
    type: Sequelize.ENUM('course', 'single'),
    allowNull: true,
    comment: '冥想子类型：课程/单节'
  },
  parent_id: {
    type: Sequelize.BIGINT,
    allowNull: true,
    comment: '父课程ID'
  },
  title: {
    type: Sequelize.STRING(255),
    allowNull: false,
    comment: '标题'
  },
  description: {
    type: Sequelize.TEXT,
    allowNull: true,
    comment: '简介'
  },
  cover_url: {
    type: Sequelize.STRING(512),
    allowNull: true,
    comment: '封面图片URL'
  },
  duration: {
    type: Sequelize.INTEGER,
    defaultValue: 0,
    comment: '时长（秒）'
  },
  tags_text: {
    type: Sequelize.STRING(255),
    allowNull: true,
    comment: '冗余标签文本，方便搜索'
  },
  audio_url: {
    type: Sequelize.STRING(512),
    allowNull: true,
    comment: '音频文件URL'
  },
  video_url: {
    type: Sequelize.STRING(512),
    allowNull: true,
    comment: '视频文件URL'
  },
  favorite_count: {
    type: Sequelize.INTEGER,
    defaultValue: 0,
    comment: '收藏人数'
  },
  status: {
    type: Sequelize.ENUM('published', 'unpublished'),
    defaultValue: 'published',
    comment: '状态：已发布/已下架'
  },
  is_recommended: {
    type: Sequelize.BOOLEAN,
    defaultValue: false,
    comment: '是否推荐'
  },
  sort_order: {
    type: Sequelize.INTEGER,
    defaultValue: 0,
    comment: '排序顺序'
  },
  created_at: {
    type: Sequelize.DATE,
    defaultValue: Sequelize.NOW,
    comment: '创建时间'
  },
  updated_at: {
    type: Sequelize.DATE,
    allowNull: true,
    comment: '更新时间'
  }
}, {
  timestamps: false,
  tableName: 'meditation_content'
})

export default MeditationContent