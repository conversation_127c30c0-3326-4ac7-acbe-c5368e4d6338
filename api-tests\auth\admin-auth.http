### 管理员认证接口测试
### 引用全局变量
< ../common/variables.http

@baseUrl = http://localhost:3004

### 1. 管理员登录 - 正常登录
POST {{baseUrl}}/admin/login
Content-Type: application/json

{
  "username": "admin",
  "password": "admin123"
}

### 2. 管理员登录 - 错误用户名
POST {{baseUrl}}/admin/login
Content-Type: application/json

{
  "username": "wrong_admin",
  "password": "{{adminPassword}}"
}

### 3. 管理员登录 - 错误密码
POST {{baseUrl}}/admin/login
Content-Type: application/json

{
  "username": "{{adminUsername}}",
  "password": "wrong_password"
}

### 4. 管理员登录 - 缺少用户名
POST {{baseUrl}}/admin/login
Content-Type: application/json

{
  "password": "{{adminPassword}}"
}

### 5. 管理员登录 - 缺少密码
POST {{baseUrl}}/admin/login
Content-Type: application/json

{
  "username": "{{adminUsername}}"
}

### 6. 管理员登录 - 空请求体
POST {{baseUrl}}/admin/login
Content-Type: application/json

{}

### 7. 获取管理员个人信息 - 有效token
GET {{baseUrl}}/admin/profile
Authorization: Bearer {{adminToken}}

### 8. 获取管理员个人信息 - 无token
GET {{baseUrl}}/admin/profile

### 9. 获取管理员个人信息 - 无效token
GET {{baseUrl}}/admin/profile
Authorization: Bearer invalid_token_here

### 10. 获取管理员个人信息 - 过期token
GET {{baseUrl}}/admin/profile
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjEiLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6InN1cGVyX2FkbWluIiwiaXNBZG1pbiI6dHJ1ZSwiaWF0IjoxNzAwMDAwMDAwLCJleHAiOjE3MDAwMDAwMDB9.expired_token

### 11. 测试token格式 - Bearer前缀缺失
GET {{baseUrl}}/admin/profile
Authorization: {{adminToken}}

### 12. 测试token格式 - 错误的Authorization头格式
GET {{baseUrl}}/admin/profile
Authorization: Token {{adminToken}}

### 13. 权限测试 - 超级管理员访问管理员列表
GET {{baseUrl}}/admin/admins
Authorization: Bearer {{adminToken}}

### 14. 并发登录测试 - 多次登录同一账户
POST {{baseUrl}}/admin/login
Content-Type: application/json

{
  "username": "{{adminUsername}}",
  "password": "{{adminPassword}}"
}

###
POST {{baseUrl}}/admin/login
Content-Type: application/json

{
  "username": "{{adminUsername}}",
  "password": "{{adminPassword}}"
}

### 15. SQL注入测试 - 用户名
POST {{baseUrl}}/admin/login
Content-Type: application/json

{
  "username": "admin' OR '1'='1",
  "password": "{{adminPassword}}"
}

### 16. SQL注入测试 - 密码
POST {{baseUrl}}/admin/login
Content-Type: application/json

{
  "username": "{{adminUsername}}",
  "password": "password' OR '1'='1"
}

### 17. XSS测试 - 用户名
POST {{baseUrl}}/admin/login
Content-Type: application/json

{
  "username": "<script>alert('xss')</script>",
  "password": "{{adminPassword}}"
}

### 18. 长字符串测试 - 用户名
POST {{baseUrl}}/admin/login
Content-Type: application/json

{
  "username": "a".repeat(1000),
  "password": "{{adminPassword}}"
}

### 19. 特殊字符测试 - 用户名
POST {{baseUrl}}/admin/login
Content-Type: application/json

{
  "username": "admin@#$%^&*()",
  "password": "{{adminPassword}}"
}

### 20. 中文字符测试 - 用户名
POST {{baseUrl}}/admin/login
Content-Type: application/json

{
  "username": "管理员",
  "password": "{{adminPassword}}"
}
