import Sequelize from 'sequelize'
import sequelize from '../lib/sequelize.js'

const UserFavorite = sequelize.define('user_favorites', {
  id: {
    type: Sequelize.BIGINT,
    primaryKey: true,
    autoIncrement: true
  },
  user_id: {
    type: Sequelize.BIGINT,
    allowNull: false
  },
  meditation_id: {
    type: Sequelize.BIGINT,
    allowNull: false
  },
  created_at: {
    type: Sequelize.DATE,
    defaultValue: Sequelize.NOW
  }
}, {
  timestamps: false,
  tableName: 'user_favorites'
})

export default UserFavorite