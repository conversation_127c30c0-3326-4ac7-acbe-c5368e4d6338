import UserLevelConfig from '../models/UserLevelConfig.js'
import { parsePaginationParams, formatPaginationResponse } from '../tool/Common.js'

export default class AdminUserLevelController {
  /**
   * @swagger
   * /admin/user-levels:
   *   get:
   *     tags:
   *       - 用户等级管理
   *     summary: 获取用户等级配置列表
   *     description: 获取所有用户等级配置
   *     security:
   *       - AdminBearer: []
   *     responses:
   *       200:
   *         description: 获取成功
   */
  static async getLevelConfigs(ctx) {
    try {
      const configs = await UserLevelConfig.findAll({
        order: [['level', 'ASC']]
      })

      ctx.body = {
        code: 200,
        data: configs.map(config => ({
          ...config.toJSON(),
          benefits: config.benefits ? JSON.parse(config.benefits) : null
        }))
      }
    } catch (error) {
      ctx.body = {
        code: 500,
        message: '获取等级配置失败',
        error: error.message
      }
    }
  }

  /**
   * @swagger
   * /admin/user-levels:
   *   post:
   *     tags:
   *       - 用户等级管理
   *     summary: 创建用户等级配置
   *     description: 创建新的用户等级配置
   *     security:
   *       - AdminBearer: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - level
   *               - level_name
   *             properties:
   *               level:
   *                 type: integer
   *                 description: 等级数字
   *               level_name:
   *                 type: string
   *                 description: 等级名称
   *               required_days:
   *                 type: integer
   *                 description: 升级所需连续天数
   *               required_duration:
   *                 type: integer
   *                 description: 升级所需总冥想时长（秒）
   *               benefits:
   *                 type: object
   *                 description: 等级权益
   *               icon_url:
   *                 type: string
   *                 description: 等级图标URL
   *     responses:
   *       200:
   *         description: 创建成功
   *       400:
   *         description: 参数错误
   */
  static async createLevelConfig(ctx) {
    const { level, level_name, required_days, required_duration, benefits, icon_url } = ctx.request.body

    if (!level || !level_name) {
      ctx.body = {
        code: 400,
        message: '等级和等级名称不能为空'
      }
      return
    }

    try {
      // 检查等级是否已存在
      const existingConfig = await UserLevelConfig.findOne({ where: { level } })
      if (existingConfig) {
        ctx.body = {
          code: 400,
          message: '该等级已存在'
        }
        return
      }

      const config = await UserLevelConfig.create({
        level,
        level_name,
        required_days: required_days || 0,
        required_duration: required_duration || 0,
        benefits: benefits ? JSON.stringify(benefits) : null,
        icon_url
      })

      ctx.body = {
        code: 200,
        message: '等级配置创建成功',
        data: {
          ...config.toJSON(),
          benefits: config.benefits ? JSON.parse(config.benefits) : null
        }
      }
    } catch (error) {
      ctx.body = {
        code: 500,
        message: '创建等级配置失败',
        error: error.message
      }
    }
  }

  /**
   * @swagger
   * /admin/user-levels/{id}:
   *   put:
   *     tags:
   *       - 用户等级管理
   *     summary: 更新用户等级配置
   *     description: 更新指定的用户等级配置
   *     security:
   *       - AdminBearer: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: integer
   *         description: 配置ID
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             properties:
   *               level_name:
   *                 type: string
   *               required_days:
   *                 type: integer
   *               required_duration:
   *                 type: integer
   *               benefits:
   *                 type: object
   *               icon_url:
   *                 type: string
   *     responses:
   *       200:
   *         description: 更新成功
   *       404:
   *         description: 配置不存在
   */
  static async updateLevelConfig(ctx) {
    const { id } = ctx.params
    const { level_name, required_days, required_duration, benefits, icon_url } = ctx.request.body

    try {
      const config = await UserLevelConfig.findByPk(id)
      if (!config) {
        ctx.body = {
          code: 404,
          message: '等级配置不存在'
        }
        return
      }

      const updateData = {}
      if (level_name !== undefined) updateData.level_name = level_name
      if (required_days !== undefined) updateData.required_days = required_days
      if (required_duration !== undefined) updateData.required_duration = required_duration
      if (benefits !== undefined) updateData.benefits = JSON.stringify(benefits)
      if (icon_url !== undefined) updateData.icon_url = icon_url

      await config.update(updateData)

      ctx.body = {
        code: 200,
        message: '等级配置更新成功',
        data: {
          ...config.toJSON(),
          benefits: config.benefits ? JSON.parse(config.benefits) : null
        }
      }
    } catch (error) {
      ctx.body = {
        code: 500,
        message: '更新等级配置失败',
        error: error.message
      }
    }
  }

  /**
   * @swagger
   * /admin/user-levels/{id}:
   *   delete:
   *     tags:
   *       - 用户等级管理
   *     summary: 删除用户等级配置
   *     description: 删除指定的用户等级配置
   *     security:
   *       - AdminBearer: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: integer
   *         description: 配置ID
   *     responses:
   *       200:
   *         description: 删除成功
   *       404:
   *         description: 配置不存在
   */
  static async deleteLevelConfig(ctx) {
    const { id } = ctx.params

    try {
      const config = await UserLevelConfig.findByPk(id)
      if (!config) {
        ctx.body = {
          code: 404,
          message: '等级配置不存在'
        }
        return
      }

      await config.destroy()

      ctx.body = {
        code: 200,
        message: '等级配置删除成功'
      }
    } catch (error) {
      ctx.body = {
        code: 500,
        message: '删除等级配置失败',
        error: error.message
      }
    }
  }
}
