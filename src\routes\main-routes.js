import KoaRouter from 'koa-router'
import controllers from '../controllers/index.js'
import meditationRoutes from './meditation-routes.js'
import adminRoutes from './admin-routes.js'

const router = new KoaRouter()

export default router
  .get('/public/get', function (ctx, next) {
    ctx.body = '禁止访问！'
  }) // 以/public开头则不经过权限认证
  .all('/upload', controllers.upload)
  .get('/public/api/:name', controllers.api.Get)
  .post('/api/:name', controllers.api.Post)
  .put('/api/:name', controllers.api.Put)
  .del('/api/:name', controllers.api.Delete)
  .post('/auth/:action', controllers.auth.Post)
  // 微信小程序相关路由
  .use('/api', meditationRoutes.routes(), meditationRoutes.allowedMethods())
  // 后台管理系统路由
  .use('/api', adminRoutes.routes(), adminRoutes.allowedMethods())
  // 兼容不带 /api 前缀的后台管理系统路由（例如 /admin/login）
  .use(adminRoutes.routes(), adminRoutes.allowedMethods())
