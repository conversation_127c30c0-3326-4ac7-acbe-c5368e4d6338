import Plant from '../models/Plant.js'
import PlantSpecies from '../models/PlantSpecies.js'
import PlantGrowthRecord from '../models/PlantGrowthRecord.js'
import EnergyRewardRule from '../models/EnergyRewardRule.js'
import sequelize from 'sequelize'
import { parsePaginationParams, formatPaginationResponse } from '../tool/Common.js'

const { Op } = sequelize

export default class AdminPlantController {
  /**
   * @swagger
   * /admin/plants/species:
   *   get:
   *     tags:
   *       - 多肉品种管理
   *     summary: 获取多肉品种列表
   *     description: 获取所有多肉品种配置
   *     security:
   *       - AdminBearer: []
   *     parameters:
   *       - in: query
   *         name: page
   *         schema:
   *           type: integer
   *           default: 1
   *         description: 页码
   *       - in: query
   *         name: limit
   *         schema:
   *           type: integer
   *           default: 10
   *         description: 每页数量
   *       - in: query
   *         name: search
   *         schema:
   *           type: string
   *         description: 搜索关键词
   *       - in: query
   *         name: rarity
   *         schema:
   *           type: string
   *           enum: [common, rare, epic, legendary]
   *         description: 稀有度筛选
   *     responses:
   *       200:
   *         description: 获取成功
   */
  static async getSpeciesList(ctx) {
    const { search, rarity } = ctx.query

    try {
      const { pageNum, pageSize, offset } = parsePaginationParams(ctx.query)
      const whereCondition = {}

      if (search) {
        whereCondition[Op.or] = [
          { name: { [Op.like]: `%${search}%` } },
          { display_name: { [Op.like]: `%${search}%` } }
        ]
      }

      if (rarity) {
        whereCondition.rarity = rarity
      }

      const species = await PlantSpecies.findAndCountAll({
        where: whereCondition,
        limit: pageSize,
        offset: offset,
        order: [['created_at', 'DESC']]
      })

      // 解析JSON字段
      const speciesList = species.rows.map(item => ({
        ...item.toJSON(),
        unlock_condition: item.unlock_condition ? JSON.parse(item.unlock_condition) : null,
        growth_stages: item.growth_stages ? JSON.parse(item.growth_stages) : null,
        image_urls: item.image_urls ? JSON.parse(item.image_urls) : null
      }))

      ctx.body = {
        code: 200,
        data: formatPaginationResponse(speciesList, species.count, pageNum, pageSize)
      }
    } catch (error) {
      ctx.body = {
        code: 500,
        message: '获取品种列表失败',
        error: error.message
      }
    }
  }

  /**
   * @swagger
   * /admin/plants/species:
   *   post:
   *     tags:
   *       - 多肉品种管理
   *     summary: 创建多肉品种
   *     description: 创建新的多肉品种配置
   *     security:
   *       - AdminBearer: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - name
   *               - display_name
   *             properties:
   *               name:
   *                 type: string
   *                 description: 品种名称（英文标识）
   *               display_name:
   *                 type: string
   *                 description: 显示名称
   *               description:
   *                 type: string
   *                 description: 品种描述
   *               rarity:
   *                 type: string
   *                 enum: [common, rare, epic, legendary]
   *                 description: 稀有度
   *               unlock_condition:
   *                 type: object
   *                 description: 解锁条件
   *               growth_stages:
   *                 type: object
   *                 description: 成长阶段配置
   *               max_level:
   *                 type: integer
   *                 description: 最大等级
   *               base_energy_per_level:
   *                 type: integer
   *                 description: 每级基础能量需求
   *               image_urls:
   *                 type: object
   *                 description: 各阶段图片URL
   *     responses:
   *       200:
   *         description: 创建成功
   */
  static async createSpecies(ctx) {
    const { 
      name, display_name, description, rarity, unlock_condition,
      growth_stages, max_level, base_energy_per_level, image_urls
    } = ctx.request.body

    if (!name || !display_name) {
      ctx.body = {
        code: 400,
        message: '品种名称和显示名称不能为空'
      }
      return
    }

    try {
      // 检查品种名称是否已存在
      const existingSpecies = await PlantSpecies.findOne({ where: { name } })
      if (existingSpecies) {
        ctx.body = {
          code: 400,
          message: '品种名称已存在'
        }
        return
      }

      const species = await PlantSpecies.create({
        name,
        display_name,
        description,
        rarity: rarity || 'common',
        unlock_condition: unlock_condition ? JSON.stringify(unlock_condition) : null,
        growth_stages: growth_stages ? JSON.stringify(growth_stages) : null,
        max_level: max_level || 10,
        base_energy_per_level: base_energy_per_level || 100,
        image_urls: image_urls ? JSON.stringify(image_urls) : null
      })

      ctx.body = {
        code: 200,
        message: '品种创建成功',
        data: {
          ...species.toJSON(),
          unlock_condition: species.unlock_condition ? JSON.parse(species.unlock_condition) : null,
          growth_stages: species.growth_stages ? JSON.parse(species.growth_stages) : null,
          image_urls: species.image_urls ? JSON.parse(species.image_urls) : null
        }
      }
    } catch (error) {
      ctx.body = {
        code: 500,
        message: '创建品种失败',
        error: error.message
      }
    }
  }

  /**
   * @swagger
   * /admin/plants/species/{id}:
   *   put:
   *     tags:
   *       - 多肉品种管理
   *     summary: 更新多肉品种
   *     description: 更新指定的多肉品种配置
   *     security:
   *       - AdminBearer: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: integer
   *         description: 品种ID
   *     responses:
   *       200:
   *         description: 更新成功
   */
  static async updateSpecies(ctx) {
    const { id } = ctx.params
    const { 
      display_name, description, rarity, unlock_condition,
      growth_stages, max_level, base_energy_per_level, image_urls, is_active
    } = ctx.request.body

    try {
      const species = await PlantSpecies.findByPk(id)
      if (!species) {
        ctx.body = {
          code: 404,
          message: '品种不存在'
        }
        return
      }

      const updateData = {}
      if (display_name !== undefined) updateData.display_name = display_name
      if (description !== undefined) updateData.description = description
      if (rarity !== undefined) updateData.rarity = rarity
      if (unlock_condition !== undefined) updateData.unlock_condition = JSON.stringify(unlock_condition)
      if (growth_stages !== undefined) updateData.growth_stages = JSON.stringify(growth_stages)
      if (max_level !== undefined) updateData.max_level = max_level
      if (base_energy_per_level !== undefined) updateData.base_energy_per_level = base_energy_per_level
      if (image_urls !== undefined) updateData.image_urls = JSON.stringify(image_urls)
      if (is_active !== undefined) updateData.is_active = is_active

      await species.update(updateData)

      ctx.body = {
        code: 200,
        message: '品种更新成功',
        data: {
          ...species.toJSON(),
          unlock_condition: species.unlock_condition ? JSON.parse(species.unlock_condition) : null,
          growth_stages: species.growth_stages ? JSON.parse(species.growth_stages) : null,
          image_urls: species.image_urls ? JSON.parse(species.image_urls) : null
        }
      }
    } catch (error) {
      ctx.body = {
        code: 500,
        message: '更新品种失败',
        error: error.message
      }
    }
  }

  /**
   * @swagger
   * /admin/plants/energy-rules:
   *   get:
   *     tags:
   *       - 能量奖励管理
   *     summary: 获取能量奖励规则列表
   *     description: 获取所有能量奖励规则，支持分页和状态筛选
   *     security:
   *       - AdminBearer: []
   *     parameters:
   *       - in: query
   *         name: page
   *         schema:
   *           type: integer
   *           default: 1
   *         description: 页码
   *       - in: query
   *         name: limit
   *         schema:
   *           type: integer
   *           default: 10
   *         description: 每页数量
   *       - in: query
   *         name: is_active
   *         schema:
   *           type: boolean
   *         description: 是否启用状态筛选
   *       - in: query
   *         name: rule_type
   *         schema:
   *           type: string
   *           enum: [meditation_complete, daily_streak, level_up, special_event]
   *         description: 规则类型筛选
   *     responses:
   *       200:
   *         description: 获取成功
   */
  static async getEnergyRules(ctx) {
    const { is_active, rule_type } = ctx.query

    try {
      const { pageNum, pageSize, offset } = parsePaginationParams(ctx.query)
      const whereCondition = {}

      if (is_active !== undefined) {
        whereCondition.is_active = is_active === 'true'
      }

      if (rule_type) {
        whereCondition.rule_type = rule_type
      }

      const rules = await EnergyRewardRule.findAndCountAll({
        where: whereCondition,
        limit: pageSize,
        offset: offset,
        order: [['priority', 'DESC'], ['created_at', 'DESC']]
      })

      const rulesList = rules.rows.map(rule => ({
        ...rule.toJSON(),
        condition: rule.condition ? JSON.parse(rule.condition) : null
      }))

      ctx.body = {
        code: 200,
        message: '获取成功',
        data: {
          list: rulesList,
          pagination: {
            page: pageNum,
            limit: pageSize,
            total: rules.count,
            pages: Math.ceil(rules.count / pageSize)
          }
        }
      }
    } catch (error) {
      ctx.body = {
        code: 500,
        message: '获取奖励规则失败',
        error: error.message
      }
    }
  }

  /**
   * @swagger
   * /admin/plants/energy-rules:
   *   post:
   *     tags:
   *       - 能量奖励管理
   *     summary: 创建能量奖励规则
   *     description: 创建新的能量奖励规则
   *     security:
   *       - AdminBearer: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - rule_name
   *               - rule_type
   *               - energy_amount
   *             properties:
   *               rule_name:
   *                 type: string
   *                 description: 规则名称
   *               rule_type:
   *                 type: string
   *                 enum: [meditation_complete, daily_streak, level_up, special_event]
   *                 description: 规则类型
   *               condition:
   *                 type: object
   *                 description: 触发条件
   *               energy_amount:
   *                 type: integer
   *                 description: 奖励能量值
   *               bonus_multiplier:
   *                 type: number
   *                 description: 奖励倍数
   *               max_daily_times:
   *                 type: integer
   *                 description: 每日最大触发次数
   *               description:
   *                 type: string
   *                 description: 规则描述
   *               priority:
   *                 type: integer
   *                 description: 优先级
   *     responses:
   *       200:
   *         description: 创建成功
   */
  static async createEnergyRule(ctx) {
    const {
      rule_name, rule_type, condition, energy_amount, bonus_multiplier,
      max_daily_times, description, priority
    } = ctx.request.body

    if (!rule_name || !rule_type || energy_amount === undefined) {
      ctx.body = {
        code: 400,
        message: '规则名称、类型和能量值不能为空'
      }
      return
    }

    try {
      const rule = await EnergyRewardRule.create({
        rule_name,
        rule_type,
        condition: condition ? JSON.stringify(condition) : null,
        energy_amount,
        bonus_multiplier: bonus_multiplier || 1.0,
        max_daily_times: max_daily_times || 0,
        description,
        priority: priority || 0
      })

      ctx.body = {
        code: 200,
        message: '奖励规则创建成功',
        data: {
          ...rule.toJSON(),
          condition: rule.condition ? JSON.parse(rule.condition) : null
        }
      }
    } catch (error) {
      ctx.body = {
        code: 500,
        message: '创建奖励规则失败',
        error: error.message
      }
    }
  }

  /**
   * @swagger
   * /admin/plants/energy-rules/{id}:
   *   get:
   *     tags:
   *       - 能量奖励管理
   *     summary: 获取能量奖励规则详情
   *     description: 根据ID获取指定的能量奖励规则详情
   *     security:
   *       - AdminBearer: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: integer
   *         description: 规则ID
   *     responses:
   *       200:
   *         description: 获取成功
   *       404:
   *         description: 规则不存在
   */
  static async getEnergyRuleDetail(ctx) {
    const { id } = ctx.params

    try {
      const rule = await EnergyRewardRule.findByPk(id)
      if (!rule) {
        ctx.body = {
          code: 404,
          message: '奖励规则不存在'
        }
        return
      }

      ctx.body = {
        code: 200,
        message: '获取成功',
        data: {
          ...rule.toJSON(),
          condition: rule.condition ? JSON.parse(rule.condition) : null
        }
      }
    } catch (error) {
      ctx.body = {
        code: 500,
        message: '获取奖励规则详情失败',
        error: error.message
      }
    }
  }

  /**
   * @swagger
   * /admin/plants/energy-rules/{id}:
   *   put:
   *     tags:
   *       - 能量奖励管理
   *     summary: 更新能量奖励规则
   *     description: 更新指定的能量奖励规则
   *     security:
   *       - AdminBearer: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: integer
   *         description: 规则ID
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             properties:
   *               rule_name:
   *                 type: string
   *                 description: 规则名称
   *               rule_type:
   *                 type: string
   *                 enum: [meditation_complete, daily_streak, level_up, special_event]
   *                 description: 规则类型
   *               condition_data:
   *                 type: object
   *                 description: 触发条件
   *               energy_amount:
   *                 type: integer
   *                 description: 奖励能量值
   *               bonus_multiplier:
   *                 type: number
   *                 description: 奖励倍数
   *               max_daily_times:
   *                 type: integer
   *                 description: 每日最大触发次数
   *               description:
   *                 type: string
   *                 description: 规则描述
   *               priority:
   *                 type: integer
   *                 description: 优先级
   *               is_active:
   *                 type: boolean
   *                 description: 是否启用
   *     responses:
   *       200:
   *         description: 更新成功
   *       404:
   *         description: 规则不存在
   */
  static async updateEnergyRule(ctx) {
    const { id } = ctx.params
    const {
      rule_name, rule_type, condition, energy_amount, bonus_multiplier,
      max_daily_times, description, priority, is_active
    } = ctx.request.body

    try {
      const rule = await EnergyRewardRule.findByPk(id)
      if (!rule) {
        ctx.body = {
          code: 404,
          message: '奖励规则不存在'
        }
        return
      }

      const updateData = {}
      if (rule_name !== undefined) updateData.rule_name = rule_name
      if (rule_type !== undefined) updateData.rule_type = rule_type
      if (condition !== undefined) updateData.condition = condition ? JSON.stringify(condition) : null
      if (energy_amount !== undefined) updateData.energy_amount = energy_amount
      if (bonus_multiplier !== undefined) updateData.bonus_multiplier = bonus_multiplier
      if (max_daily_times !== undefined) updateData.max_daily_times = max_daily_times
      if (description !== undefined) updateData.description = description
      if (priority !== undefined) updateData.priority = priority
      if (is_active !== undefined) updateData.is_active = is_active
      updateData.updated_at = new Date()

      await rule.update(updateData)

      ctx.body = {
        code: 200,
        message: '奖励规则更新成功',
        data: {
          ...rule.toJSON(),
          condition: rule.condition ? JSON.parse(rule.condition) : null
        }
      }
    } catch (error) {
      ctx.body = {
        code: 500,
        message: '更新奖励规则失败',
        error: error.message
      }
    }
  }

  /**
   * @swagger
   * /admin/plants/energy-rules/{id}/toggle:
   *   put:
   *     tags:
   *       - 能量奖励管理
   *     summary: 切换能量奖励规则状态
   *     description: 启用或禁用指定的能量奖励规则
   *     security:
   *       - AdminBearer: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: integer
   *         description: 规则ID
   *     responses:
   *       200:
   *         description: 状态切换成功
   *       404:
   *         description: 规则不存在
   */
  static async toggleEnergyRuleStatus(ctx) {
    const { id } = ctx.params

    try {
      const rule = await EnergyRewardRule.findByPk(id)
      if (!rule) {
        ctx.body = {
          code: 404,
          message: '奖励规则不存在'
        }
        return
      }

      await rule.update({
        is_active: !rule.is_active,
        updated_at: new Date()
      })

      ctx.body = {
        code: 200,
        message: `奖励规则已${rule.is_active ? '启用' : '禁用'}`,
        data: {
          ...rule.toJSON(),
          condition: rule.condition ? JSON.parse(rule.condition) : null
        }
      }
    } catch (error) {
      ctx.body = {
        code: 500,
        message: '切换奖励规则状态失败',
        error: error.message
      }
    }
  }

  /**
   * @swagger
   * /admin/plants/energy-rules/{id}:
   *   delete:
   *     tags:
   *       - 能量奖励管理
   *     summary: 删除能量奖励规则
   *     description: 删除指定的能量奖励规则
   *     security:
   *       - AdminBearer: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: integer
   *         description: 规则ID
   *     responses:
   *       200:
   *         description: 删除成功
   *       404:
   *         description: 规则不存在
   */
  static async deleteEnergyRule(ctx) {
    const { id } = ctx.params

    try {
      const rule = await EnergyRewardRule.findByPk(id)
      if (!rule) {
        ctx.body = {
          code: 404,
          message: '奖励规则不存在'
        }
        return
      }

      await rule.destroy()

      ctx.body = {
        code: 200,
        message: '奖励规则删除成功'
      }
    } catch (error) {
      ctx.body = {
        code: 500,
        message: '删除奖励规则失败',
        error: error.message
      }
    }
  }

  /**
   * @swagger
   * /admin/plants/statistics:
   *   get:
   *     tags:
   *       - 多肉统计管理
   *     summary: 获取多肉系统统计数据
   *     description: 获取多肉养成系统的统计数据
   *     security:
   *       - AdminBearer: []
   *     responses:
   *       200:
   *         description: 获取成功
   */
  static async getPlantStatistics(ctx) {
    try {
      // 多肉总数
      const totalPlants = await Plant.count()

      // 等级分布
      const levelDistribution = await Plant.findAll({
        attributes: [
          'level',
          [Plant.sequelize.fn('COUNT', Plant.sequelize.col('id')), 'count']
        ],
        group: ['level'],
        order: [['level', 'ASC']],
        raw: true
      })

      // 品种分布
      const speciesDistribution = await Plant.findAll({
        attributes: [
          'species',
          [Plant.sequelize.fn('COUNT', Plant.sequelize.col('id')), 'count']
        ],
        group: ['species'],
        order: [[Plant.sequelize.fn('COUNT', Plant.sequelize.col('id')), 'DESC']],
        raw: true
      })

      // 最近7天的成长记录统计
      const sevenDaysAgo = new Date()
      sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7)

      const recentGrowthRecords = await PlantGrowthRecord.findAll({
        attributes: [
          [PlantGrowthRecord.sequelize.fn('DATE', PlantGrowthRecord.sequelize.col('created_at')), 'date'],
          [PlantGrowthRecord.sequelize.fn('COUNT', PlantGrowthRecord.sequelize.col('id')), 'count'],
          [PlantGrowthRecord.sequelize.fn('SUM', PlantGrowthRecord.sequelize.col('change_value')), 'total_energy']
        ],
        where: {
          created_at: {
            [Op.gte]: sevenDaysAgo
          }
        },
        group: [PlantGrowthRecord.sequelize.fn('DATE', PlantGrowthRecord.sequelize.col('created_at'))],
        order: [[PlantGrowthRecord.sequelize.fn('DATE', PlantGrowthRecord.sequelize.col('created_at')), 'ASC']],
        raw: true
      })

      // 活跃用户数（最近7天有多肉成长记录的用户）
      const activePlantUsers = await PlantGrowthRecord.findAll({
        attributes: [
          [PlantGrowthRecord.sequelize.fn('COUNT', PlantGrowthRecord.sequelize.fn('DISTINCT', PlantGrowthRecord.sequelize.col('plant_id'))), 'count']
        ],
        where: {
          created_at: {
            [Op.gte]: sevenDaysAgo
          }
        },
        raw: true
      })

      ctx.body = {
        code: 200,
        data: {
          total_plants: totalPlants,
          level_distribution: levelDistribution,
          species_distribution: speciesDistribution,
          recent_growth_records: recentGrowthRecords,
          active_plant_users: activePlantUsers[0]?.count || 0
        }
      }
    } catch (error) {
      ctx.body = {
        code: 500,
        message: '获取多肉统计数据失败',
        error: error.message
      }
    }
  }

  /**
   * @swagger
   * /admin/plants/species/{id}:
   *   delete:
   *     tags:
   *       - 多肉品种管理
   *     summary: 删除多肉品种
   *     description: 删除指定的多肉品种配置
   *     security:
   *       - AdminBearer: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: integer
   *         description: 品种ID
   *     responses:
   *       200:
   *         description: 删除成功
   *       400:
   *         description: 品种正在使用中，无法删除
   *       404:
   *         description: 品种不存在
   */
  static async deleteSpecies(ctx) {
    const { id } = ctx.params

    try {
      const species = await PlantSpecies.findByPk(id)
      if (!species) {
        ctx.body = {
          code: 404,
          message: '品种不存在'
        }
        return
      }

      // 检查是否有用户多肉正在使用该品种
      const usageCount = await Plant.count({
        where: { species: species.name }
      })

      if (usageCount > 0) {
        ctx.body = {
          code: 400,
          message: `该品种正在被 ${usageCount} 个多肉使用，无法删除`
        }
        return
      }

      await species.destroy()

      ctx.body = {
        code: 200,
        message: '品种删除成功'
      }
    } catch (error) {
      ctx.body = {
        code: 500,
        message: '删除品种失败',
        error: error.message
      }
    }
  }

  /**
   * @swagger
   * /admin/plants/species/batch-delete:
   *   delete:
   *     tags:
   *       - 多肉品种管理
   *     summary: 批量删除多肉品种
   *     description: 批量删除多个多肉品种配置
   *     security:
   *       - AdminBearer: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - species_ids
   *             properties:
   *               species_ids:
   *                 type: array
   *                 items:
   *                   type: integer
   *                 description: 要删除的品种ID数组
   *     responses:
   *       200:
   *         description: 批量删除成功
   *       400:
   *         description: 部分品种正在使用中
   */
  static async batchDeleteSpecies(ctx) {
    const { species_ids } = ctx.request.body

    if (!species_ids || !Array.isArray(species_ids) || species_ids.length === 0) {
      ctx.body = {
        code: 400,
        message: '请提供要删除的品种ID数组'
      }
      return
    }

    try {
      // 获取所有要删除的品种信息
      const speciesList = await PlantSpecies.findAll({
        where: { id: { [Op.in]: species_ids } }
      })

      if (speciesList.length === 0) {
        ctx.body = {
          code: 404,
          message: '未找到要删除的品种'
        }
        return
      }

      // 检查哪些品种正在使用
      const speciesNames = speciesList.map(species => species.name)
      const usedSpecies = await Plant.findAll({
        where: { species: { [Op.in]: speciesNames } },
        attributes: ['species'],
        group: ['species']
      })

      const usedSpeciesNames = usedSpecies.map(item => item.species)
      const canDeleteSpecies = speciesList.filter(species => !usedSpeciesNames.includes(species.name))

      if (canDeleteSpecies.length === 0) {
        ctx.body = {
          code: 400,
          message: '所选品种都在使用中，无法删除'
        }
        return
      }

      // 删除未使用的品种
      const canDeleteIds = canDeleteSpecies.map(species => species.id)
      const deletedCount = await PlantSpecies.destroy({
        where: { id: { [Op.in]: canDeleteIds } }
      })

      ctx.body = {
        code: 200,
        message: `成功删除 ${deletedCount} 个品种`,
        data: {
          deleted_count: deletedCount,
          used_species_names: usedSpeciesNames
        }
      }
    } catch (error) {
      ctx.body = {
        code: 500,
        message: '批量删除品种失败',
        error: error.message
      }
    }
  }


}
