import sequelizePkg from 'sequelize'
import sequelize from '../lib/sequelize.js'

const { DataTypes } = sequelizePkg

const UserLevelConfig = sequelize.define('UserLevelConfig', {
  id: {
    type: DataTypes.BIGINT,
    primaryKey: true,
    autoIncrement: true
  },
  level: {
    type: DataTypes.INTEGER,
    allowNull: false,
    unique: true,
    comment: '等级'
  },
  level_name: {
    type: DataTypes.STRING(64),
    allowNull: false,
    comment: '等级名称'
  },
  required_days: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    comment: '升级所需连续天数'
  },
  required_duration: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    comment: '升级所需总冥想时长（秒）'
  },
  benefits: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '等级权益描述（JSON格式）'
  },
  icon_url: {
    type: DataTypes.STRING(512),
    allowNull: true,
    comment: '等级图标URL'
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW,
    comment: '创建时间'
  },
  updated_at: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '更新时间'
  }
}, {
  tableName: 'user_level_configs',
  timestamps: false,
  comment: '用户等级配置表'
})

export default UserLevelConfig
