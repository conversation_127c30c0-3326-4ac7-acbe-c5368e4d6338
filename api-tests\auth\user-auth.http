### 用户认证接口测试（微信小程序）
### 引用全局变量
< ../common/variables.http

### 1. 微信code换取session - 正常流程
POST {{apiUrl}}/public/user/code2session
Content-Type: application/json

{
  "code": "{{testWxCode}}",
  "appId": "{{testWxAppId}}",
  "appSecret": "{{testWxAppSecret}}"
}

### 2. 微信code换取session - 无效code
POST {{apiUrl}}/public/user/code2session
Content-Type: application/json

{
  "code": "invalid_wx_code",
  "appId": "{{testWxAppId}}",
  "appSecret": "{{testWxAppSecret}}"
}

### 3. 微信code换取session - 缺少code
POST {{apiUrl}}/public/user/code2session
Content-Type: application/json

{
  "appId": "{{testWxAppId}}",
  "appSecret": "{{testWxAppSecret}}"
}

### 4. 微信code换取session - 缺少appId
POST {{apiUrl}}/public/user/code2session
Content-Type: application/json

{
  "code": "{{testWxCode}}",
  "appSecret": "{{testWxAppSecret}}"
}

### 5. 微信用户登录 - 正常登录
POST {{apiUrl}}/public/user/login
Content-Type: application/json

{
  "openid": "{{testUserOpenId}}",
  "nickname": "测试用户",
  "avatar": "https://example.com/avatar.jpg",
  "gender": 1,
  "city": "深圳",
  "province": "广东",
  "country": "中国"
}

> {%
  if (response.status === 200) {
    client.global.set("testUserToken", response.body.data.token);
    console.log("用户登录成功，token已保存");
  }
%}

### 6. 微信用户登录 - 缺少openid
POST {{apiUrl}}/public/user/login
Content-Type: application/json

{
  "nickname": "测试用户",
  "avatar": "https://example.com/avatar.jpg"
}

### 7. 微信用户登录 - 最小信息登录
POST {{apiUrl}}/public/user/login
Content-Type: application/json

{
  "openid": "{{testUserOpenId}}_minimal"
}

### 8. 微信用户登录 - 完整信息登录
POST {{apiUrl}}/public/user/login
Content-Type: application/json

{
  "openid": "{{testUserOpenId}}_full",
  "nickname": "完整信息用户",
  "avatar": "https://example.com/full-avatar.jpg",
  "gender": 2,
  "city": "北京",
  "province": "北京",
  "country": "中国",
  "language": "zh_CN"
}

### 9. 获取用户基础信息 - 有效token
GET {{apiUrl}}/user/profile
Authorization: Bearer {{testUserToken}}

### 10. 获取用户基础信息 - 无token
GET {{apiUrl}}/user/profile

### 11. 获取用户基础信息 - 无效token
GET {{apiUrl}}/user/profile
Authorization: Bearer invalid_user_token

### 12. 重复登录测试 - 同一openid多次登录
POST {{apiUrl}}/public/user/login
Content-Type: application/json

{
  "openid": "{{testUserOpenId}}",
  "nickname": "更新后的用户名",
  "avatar": "https://example.com/new-avatar.jpg"
}

### 13. 特殊字符测试 - nickname
POST {{apiUrl}}/public/user/login
Content-Type: application/json

{
  "openid": "{{testUserOpenId}}_special",
  "nickname": "特殊字符用户@#$%^&*()",
  "avatar": "https://example.com/avatar.jpg"
}

### 14. 长字符串测试 - nickname
POST {{apiUrl}}/public/user/login
Content-Type: application/json

{
  "openid": "{{testUserOpenId}}_long",
  "nickname": "很长很长的用户名".repeat(10),
  "avatar": "https://example.com/avatar.jpg"
}

### 15. XSS测试 - nickname
POST {{apiUrl}}/public/user/login
Content-Type: application/json

{
  "openid": "{{testUserOpenId}}_xss",
  "nickname": "<script>alert('xss')</script>",
  "avatar": "https://example.com/avatar.jpg"
}

### 16. SQL注入测试 - openid
POST {{apiUrl}}/public/user/login
Content-Type: application/json

{
  "openid": "test' OR '1'='1",
  "nickname": "SQL注入测试用户"
}

### 17. 无效URL测试 - avatar
POST {{apiUrl}}/public/user/login
Content-Type: application/json

{
  "openid": "{{testUserOpenId}}_invalid_avatar",
  "nickname": "无效头像用户",
  "avatar": "not_a_valid_url"
}

### 18. 空字符串测试 - 各字段
POST {{apiUrl}}/public/user/login
Content-Type: application/json

{
  "openid": "{{testUserOpenId}}_empty",
  "nickname": "",
  "avatar": "",
  "city": "",
  "province": "",
  "country": ""
}

### 19. 数字类型测试 - gender
POST {{apiUrl}}/public/user/login
Content-Type: application/json

{
  "openid": "{{testUserOpenId}}_gender",
  "nickname": "性别测试用户",
  "gender": "invalid_gender"
}

### 20. 并发登录测试 - 多个用户同时登录
POST {{apiUrl}}/public/user/login
Content-Type: application/json

{
  "openid": "concurrent_user_1",
  "nickname": "并发用户1"
}

###
POST {{apiUrl}}/public/user/login
Content-Type: application/json

{
  "openid": "concurrent_user_2",
  "nickname": "并发用户2"
}
