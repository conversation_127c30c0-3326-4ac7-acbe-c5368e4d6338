import Sequelize from 'sequelize'
import sequelize from '../lib/sequelize.js'

const PlanHistory = sequelize.define('plan_history', {
  id: {
    type: Sequelize.BIGINT,
    primaryKey: true,
    autoIncrement: true
  },
  user_id: {
    type: Sequelize.BIGINT,
    allowNull: false
  },
  meditation_id: {
    type: Sequelize.BIGINT,
    allowNull: false
  },
  completed_at: {
    type: Sequelize.DATE,
    defaultValue: Sequelize.NOW
  }
}, {
  timestamps: false,
  tableName: 'plan_history'
})

export default PlanHistory