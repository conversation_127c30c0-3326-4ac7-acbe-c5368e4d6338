import Sequelize from 'sequelize'
import sequelize from '../lib/sequelize.js'

const UserMeditationStats = sequelize.define('user_meditation_stats', {
  id: {
    type: Sequelize.BIGINT,
    primaryKey: true,
    autoIncrement: true
  },
  user_id: {
    type: Sequelize.BIGINT,
    allowNull: false
  },
  period_type: {
    type: Sequelize.ENUM('day', 'week', 'month', 'year'),
    allowNull: false
  },
  period_date: {
    type: Sequelize.DATEONLY,
    allowNull: false,
    comment: '周期开始日期'
  },
  meditation_duration: {
    type: Sequelize.INTEGER,
    defaultValue: 0,
    comment: '冥想时长(秒)'
  },
  energy_gained: {
    type: Sequelize.INTEGER,
    defaultValue: 0,
    comment: '获取能量值'
  },
  tasks_completed: {
    type: Sequelize.INTEGER,
    defaultValue: 0,
    comment: '完成任务数'
  },
  created_at: {
    type: Sequelize.DATE,
    defaultValue: Sequelize.NOW
  }
}, {
  timestamps: false,
  tableName: 'user_meditation_stats'
})

export default UserMeditationStats