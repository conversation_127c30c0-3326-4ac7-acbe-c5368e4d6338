import jwt from 'jsonwebtoken'
import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'
import Admin from '../models/Admin.js'

// 在ES模块中获取__dirname的等价物
const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

const privateKey = fs.readFileSync(path.join(__dirname, '../../publicKey.pub'))

/**
 * 管理员认证中间件
 */
export const adminAuth = async (ctx, next) => {
  const token = ctx.headers.authorization?.replace('Bearer ', '')
  
  if (!token) {
    ctx.status = 401
    ctx.body = {
      code: 401,
      message: '未提供认证token'
    }
    return
  }

  try {
    const decoded = jwt.verify(token, privateKey)
    
    // 验证是否为管理员token
    if (!decoded.isAdmin) {
      ctx.status = 401
      ctx.body = {
        code: 401,
        message: '无效的管理员token'
      }
      return
    }

    // 查询管理员信息
    const admin = await Admin.findByPk(decoded.id)
    if (!admin || admin.status !== 'active') {
      ctx.status = 401
      ctx.body = {
        code: 401,
        message: '管理员账户不存在或已被禁用'
      }
      return
    }

    // 将管理员信息存储到ctx.state中
    ctx.state.admin = {
      id: admin.id,
      username: admin.username,
      role: admin.role
    }

    await next()
  } catch (error) {
    ctx.status = 401
    ctx.body = {
      code: 401,
      message: 'token验证失败',
      error: error.message
    }
  }
}

/**
 * 权限检查中间件
 * @param {Array} allowedRoles 允许的角色列表
 */
export const checkPermission = (allowedRoles = []) => {
  return async (ctx, next) => {
    const admin = ctx.state.admin
    
    if (!admin) {
      ctx.status = 401
      ctx.body = {
        code: 401,
        message: '未认证的管理员'
      }
      return
    }

    // 超级管理员拥有所有权限
    if (admin.role === 'super_admin') {
      await next()
      return
    }

    // 检查角色权限
    if (allowedRoles.length > 0 && !allowedRoles.includes(admin.role)) {
      ctx.status = 403
      ctx.body = {
        code: 403,
        message: '权限不足'
      }
      return
    }

    await next()
  }
}
