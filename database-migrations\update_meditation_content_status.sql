-- 修改冥想内容状态字段
-- 将状态从 draft/published/archived 改为 published/unpublished
--
-- 使用说明：
-- 1. 建议在执行前先备份整个数据库
-- 2. 可以逐步执行每个步骤，检查结果后再继续
-- 3. 如果遇到问题，可以使用备份表恢复数据

-- 开始事务（可选，如果想要原子操作）
-- START TRANSACTION;

-- 1. 首先备份当前数据
DROP TABLE IF EXISTS meditation_content_status_backup;
CREATE TABLE meditation_content_status_backup AS
SELECT id, status, created_at FROM meditation_content;

-- 验证备份
SELECT
    '备份验证' as step,
    COUNT(*) as backup_count,
    (SELECT COUNT(*) FROM meditation_content) as original_count
FROM meditation_content_status_backup;

-- 3. 数据迁移逻辑
-- draft -> unpublished (草稿变为已下架)
-- published -> published (已发布保持已发布)
-- archived -> unpublished (已归档变为已下架)

-- 方法1：分别更新每种状态（推荐）
UPDATE meditation_content
SET new_status = 'published'
WHERE status = 'published' AND id > 0;

UPDATE meditation_content
SET new_status = 'unpublished'
WHERE status = 'draft' AND id > 0;

UPDATE meditation_content
SET new_status = 'unpublished'
WHERE status = 'archived' AND id > 0;

-- 方法2：如果你想使用单个UPDATE语句，需要先禁用安全模式
-- SET SQL_SAFE_UPDATES = 0;
-- UPDATE meditation_content
-- SET new_status = CASE
--     WHEN status = 'published' THEN 'published'
--     WHEN status = 'draft' THEN 'unpublished'
--     WHEN status = 'archived' THEN 'unpublished'
--     ELSE 'unpublished'
-- END;
-- SET SQL_SAFE_UPDATES = 1;

-- 4. 删除旧的状态字段
ALTER TABLE meditation_content DROP COLUMN status;

-- 5. 重命名新字段为原字段名
ALTER TABLE meditation_content CHANGE COLUMN new_status status ENUM('published', 'unpublished') DEFAULT 'published' COMMENT '状态：已发布/已下架';

-- 6. 验证数据迁移结果
SELECT
    '迁移后状态分布' as step,
    status,
    COUNT(*) as count,
    CONCAT(ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM meditation_content), 2), '%') as percentage
FROM meditation_content
GROUP BY status;

-- 7. 对比迁移前后的数据
SELECT
    '迁移前后对比' as step,
    'before' as period,
    status,
    COUNT(*) as count
FROM meditation_content_status_backup
GROUP BY status
UNION ALL
SELECT
    '迁移前后对比' as step,
    'after' as period,
    status,
    COUNT(*) as count
FROM meditation_content
GROUP BY status
ORDER BY period, status;

-- 8. 验证数据完整性
SELECT
    '数据完整性检查' as step,
    CASE
        WHEN (SELECT COUNT(*) FROM meditation_content) = (SELECT COUNT(*) FROM meditation_content_status_backup)
        THEN '✅ 记录数量一致'
        ELSE '❌ 记录数量不一致'
    END as record_count_check,
    CASE
        WHEN (SELECT COUNT(*) FROM meditation_content WHERE status NOT IN ('published', 'unpublished')) = 0
        THEN '✅ 所有状态值都有效'
        ELSE '❌ 存在无效状态值'
    END as status_validation;

-- 9. 显示迁移完成信息
SELECT
    '🎉 数据迁移完成' as message,
    COUNT(*) as total_records,
    SUM(CASE WHEN status = 'published' THEN 1 ELSE 0 END) as published_count,
    SUM(CASE WHEN status = 'unpublished' THEN 1 ELSE 0 END) as unpublished_count,
    CONCAT('备份表: meditation_content_status_backup') as backup_info
FROM meditation_content;

-- 提交事务（如果使用了事务）
-- COMMIT;

-- 如果需要回滚，可以使用以下语句：
-- ROLLBACK;
-- 或者使用备份表恢复：
-- UPDATE meditation_content mc
-- JOIN meditation_content_status_backup backup ON mc.id = backup.id
-- SET mc.status = backup.status
-- WHERE mc.id > 0;
