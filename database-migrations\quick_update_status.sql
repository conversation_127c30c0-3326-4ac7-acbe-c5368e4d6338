-- 快速状态字段更新脚本
-- 适用于MySQL安全更新模式

-- 1. 备份数据
CREATE TABLE IF NOT EXISTS meditation_content_status_backup AS 
SELECT id, status, created_at FROM meditation_content;

-- 2. 添加新状态字段
ALTER TABLE meditation_content 
ADD COLUMN new_status ENUM('published', 'unpublished') DEFAULT 'published' COMMENT '状态：已发布/已下架';

-- 3. 分别更新每种状态（避免安全更新模式限制）
UPDATE meditation_content 
SET new_status = 'published' 
WHERE status = 'published' AND id > 0;

UPDATE meditation_content 
SET new_status = 'unpublished' 
WHERE status = 'draft' AND id > 0;

UPDATE meditation_content 
SET new_status = 'unpublished' 
WHERE status = 'archived' AND id > 0;

-- 4. 删除旧字段
ALTER TABLE meditation_content DROP COLUMN status;

-- 5. 重命名新字段
ALTER TABLE meditation_content CHANGE COLUMN new_status status ENUM('published', 'unpublished') DEFAULT 'published' COMMENT '状态：已发布/已下架';

-- 6. 验证结果
SELECT 
    status,
    COUNT(*) as count
FROM meditation_content 
GROUP BY status;
