import User from '../models/User.js'
import Plant from '../models/Plant.js'
import UserMeditationStats from '../models/UserMeditationStats.js'
import UserFavorite from '../models/UserFavorite.js'
import MeditationContent from '../models/MeditationContent.js'
import WeChatService from '../services/wechat.js'
import jwt from 'jsonwebtoken'
import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'
import sequelize from 'sequelize'
import { parsePaginationParams, formatPaginationResponse } from '../tool/Common.js'

// 在ES模块中获取__dirname的等价物
const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

const { Op } = sequelize

const privateKey = fs.readFileSync(path.join(__dirname, '../../publicKey.pub'))

export default class UserController {
  /**
   * @swagger
   * /public/user/code2session:
   *   post:
   *     tags:
   *       - 用户模块
   *     summary: 微信code换取openid
   *     description: 通过微信小程序登录时获取的code换取openid和session_key
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - code
   *             properties:
   *               code:
   *                 type: string
   *                 description: 微信小程序登录时获取的code
   *                 example: "081234567890abcdef"
   *     responses:
   *       200:
   *         description: 获取成功
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 code:
   *                   type: integer
   *                   example: 200
   *                 message:
   *                   type: string
   *                   example: "获取openid成功"
   *                 data:
   *                   type: object
   *                   properties:
   *                     openid:
   *                       type: string
   *                       description: 微信openid
   *                       example: "openid_test_123"
   *                     session_key:
   *                       type: string
   *                       description: 会话密钥
   *                       example: "session_key_123"
   *                     unionid:
   *                       type: string
   *                       description: 微信unionid（可能为空）
   *                       example: "unionid_test_123"
   *       400:
   *         description: 请求参数错误
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ApiResponse'
   *       500:
   *         description: 服务器内部错误
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ApiResponse'
   */
  static async code2Session (ctx) {
    const { code } = ctx.request.body

    if (!code) {
      ctx.body = {
        code: 400,
        message: 'code不能为空'
      }
      return
    }

    try {
      const result = await WeChatService.code2Session(code)

      ctx.body = {
        code: 200,
        message: '获取openid成功',
        data: result
      }
    } catch (error) {
      ctx.body = {
        code: 500,
        message: '获取openid失败',
        error: error.message
      }
    }
  }

  /**
   * @swagger
   * /public/user/login:
   *   post:
   *     tags:
   *       - 用户模块
   *     summary: 微信小程序用户登录
   *     description: 微信小程序用户登录，支持新用户自动注册
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - openid
   *             properties:
   *               openid:
   *                 type: string
   *                 description: 微信openid
   *                 example: "openid_test_123"
   *               unionid:
   *                 type: string
   *                 description: 微信unionid
   *                 example: "unionid_test_123"
   *               nickname:
   *                 type: string
   *                 description: 用户昵称
   *                 example: "欢欢"
   *               avatar_url:
   *                 type: string
   *                 description: 头像URL
   *                 example: "https://example.com/avatar.jpg"
   *     responses:
   *       200:
   *         description: 登录成功
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 code:
   *                   type: integer
   *                   example: 200
   *                 message:
   *                   type: string
   *                   example: "登录成功"
   *                 data:
   *                   type: object
   *                   properties:
   *                     token:
   *                       type: string
   *                       description: JWT token
   *                       example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
   *                     user:
   *                       $ref: '#/components/schemas/User'
   *       400:
   *         description: 请求参数错误
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ApiResponse'
   *       500:
   *         description: 服务器内部错误
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ApiResponse'
   */
  static async login (ctx) {
    const { openid, unionid, nickname, avatar_url } = ctx.request.body

    if (!openid) {
      ctx.body = {
        code: 400,
        message: 'openid不能为空'
      }
      return
    }

    try {
      let user = await User.findOne({ where: { openid } })

      if (!user) {
        // 新用户注册
        user = await User.create({
          openid,
          unionid,
          nickname,
          avatar_url,
          meditation_level: 1,
          streak_days: 0
        })

        // 为新用户创建初始多肉
        await Plant.create({
          user_id: user.id,
          species: 'succulent',
          energy_value: 0,
          level: 1
        })
      } else {
        // 更新用户信息
        await user.update({
          unionid: unionid || user.unionid,
          nickname: nickname || user.nickname,
          avatar_url: avatar_url || user.avatar_url
        })
      }

      // 生成JWT token
      const token = jwt.sign(
        {
          id: user.id,
          openid: user.openid
        },
        privateKey,
        { expiresIn: '30d' }
      )

      ctx.body = {
        code: 200,
        message: '登录成功',
        data: {
          token,
          user: {
            id: user.id,
            openid: user.openid,
            nickname: user.nickname,
            avatar_url: user.avatar_url,
            meditation_level: user.meditation_level,
            streak_days: user.streak_days
          }
        },
      }
    } catch (error) {
      ctx.body = {
        code: 500,
        message: '登录失败',
        error: error.message
      }
    }
  }

  /**
   * @swagger
   * /user/profile:
   *   get:
   *     tags:
   *       - 用户模块
   *     summary: 获取用户基础信息
   *     description: 获取当前登录用户的基础信息
   *     security:
   *       - Bearer: []
   *     responses:
   *       200:
   *         description: 获取成功
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 code:
   *                   type: integer
   *                   example: 200
   *                 data:
   *                   type: object
   *                   properties:
   *                     meditation_level:
   *                       type: integer
   *                       description: 冥想等级
   *                       example: 2
   *                     streak_days:
   *                       type: integer
   *                       description: 连续天数
   *                       example: 5
   *                     plant_count:
   *                       type: integer
   *                       description: 多肉数量
   *                       example: 2
   *       500:
   *         description: 服务器内部错误
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ApiResponse'
   */
  static async getProfile (ctx) {
    const userId = ctx.state.user.id

    try {
      const user = await User.findByPk(userId)
      const plantCount = await Plant.count({ where: { user_id: userId } })

      ctx.body = {
        code: 200,
        data: {
          meditation_level: user.meditation_level,
          streak_days: user.streak_days,
          plant_count: plantCount
        }
      }
    } catch (error) {
      ctx.body = {
        code: 500,
        message: '获取用户信息失败',
        error: error.message
      }
    }
  }

  /**
   * @swagger
   * /user/favorites:
   *   get:
   *     tags:
   *       - 用户模块
   *     summary: 获取用户收藏列表
   *     description: 获取当前用户收藏的冥想内容列表
   *     security:
   *       - Bearer: []
   *     parameters:
   *       - in: query
   *         name: page
   *         schema:
   *           type: integer
   *           default: 1
   *         description: 页码
   *       - in: query
   *         name: limit
   *         schema:
   *           type: integer
   *           default: 10
   *         description: 每页数量
   *     responses:
   *       200:
   *         description: 获取成功
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/PaginatedResponse'
   *       500:
   *         description: 服务器内部错误
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ApiResponse'
   */
  static async getFavorites (ctx) {
    const userId = ctx.state.user.id

    try {
      const { pageNum, pageSize, offset } = parsePaginationParams(ctx.query)
      const favorites = await UserFavorite.findAndCountAll({
        where: { user_id: userId },
        include: [
          {
            model: MeditationContent,
            as: 'meditation'
          }
        ],
        limit: pageSize,
        offset: offset,
        order: [['created_at', 'DESC']]
      })

      ctx.body = {
        code: 200,
        data: formatPaginationResponse(favorites.rows, favorites.count, pageNum, pageSize)
      }
    } catch (error) {
      ctx.body = {
        code: 500,
        message: '获取收藏列表失败',
        error: error.message
      }
    }
  }

  /**
   * @swagger
   * /user/plants:
   *   get:
   *     tags:
   *       - 用户模块
   *     summary: 获取用户多肉列表
   *     description: 获取当前用户的多肉列表
   *     security:
   *       - Bearer: []
   *     responses:
   *       200:
   *         description: 获取成功
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 code:
   *                   type: integer
   *                   example: 200
   *                 data:
   *                   type: array
   *                   items:
   *                     $ref: '#/components/schemas/Plant'
   *       500:
   *         description: 服务器内部错误
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ApiResponse'
   */
  static async getPlants (ctx) {
    const userId = ctx.state.user.id

    try {
      const plants = await Plant.findAll({
        where: { user_id: userId },
        order: [['created_at', 'ASC']]
      })

      ctx.body = {
        code: 200,
        data: plants
      }
    } catch (error) {
      ctx.body = {
        code: 500,
        message: '获取多肉列表失败',
        error: error.message
      }
    }
  }

  /**
   * @swagger
   * /user/meditation-stats:
   *   get:
   *     tags:
   *       - 用户模块
   *     summary: 获取冥想统计数据
   *     description: 获取用户的冥想统计数据
   *     security:
   *       - Bearer: []
   *     parameters:
   *       - in: query
   *         name: period_type
   *         required: true
   *         schema:
   *           type: string
   *           enum: [day, week, month, year]
   *         description: 统计周期类型
   *       - in: query
   *         name: start_date
   *         schema:
   *           type: string
   *           format: date
   *         description: 开始日期
   *       - in: query
   *         name: end_date
   *         schema:
   *           type: string
   *           format: date
   *         description: 结束日期
   *     responses:
   *       200:
   *         description: 获取成功
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 code:
   *                   type: integer
   *                   example: 200
   *                 data:
   *                   type: array
   *                   items:
   *                     type: object
   *                     properties:
   *                       user_id:
   *                         type: integer
   *                         description: 用户ID
   *                       period_type:
   *                         type: string
   *                         description: 周期类型
   *                       period_date:
   *                         type: string
   *                         format: date
   *                         description: 统计日期
   *                       meditation_count:
   *                         type: integer
   *                         description: 冥想次数
   *                       total_duration:
   *                         type: integer
   *                         description: 总时长
   *       400:
   *         description: 请求参数错误
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ApiResponse'
   *       500:
   *         description: 服务器内部错误
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ApiResponse'
   */
  static async getMeditationStats (ctx) {
    const userId = ctx.state.user.id
    const { period_type, start_date, end_date } = ctx.query

    if (
      !period_type ||
      !['day', 'week', 'month', 'year'].includes(period_type)
    ) {
      ctx.body = {
        code: 400,
        message: '请提供有效的周期类型 (day/week/month/year)'
      }
      return
    }

    try {
      const whereCondition = {
        user_id: userId,
        period_type
      }

      if (start_date && end_date) {
        whereCondition.period_date = {
          [Op.between]: [start_date, end_date]
        }
      }

      const stats = await UserMeditationStats.findAll({
        where: whereCondition,
        order: [['period_date', 'ASC']]
      })

      ctx.body = {
        code: 200,
        data: stats
      }
    } catch (error) {
      ctx.body = {
        code: 500,
        message: '获取冥想统计失败',
        error: error.message
      }
    }
  }
}
