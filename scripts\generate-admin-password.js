/**
 * 管理员密码生成工具
 * 用于生成bcrypt加密的管理员密码
 * 
 * 使用方法：
 * node scripts/generate-admin-password.js [password]
 * 
 * 如果不提供密码参数，将使用默认密码 "admin123"
 */

import bcrypt from 'bcrypt'

async function generatePassword(password = 'admin123') {
  try {
    const saltRounds = 10
    const hashedPassword = await bcrypt.hash(password, saltRounds)
    
    console.log('='.repeat(50))
    console.log('管理员密码生成工具')
    console.log('='.repeat(50))
    console.log(`原始密码: ${password}`)
    console.log(`加密密码: ${hashedPassword}`)
    console.log('='.repeat(50))
    console.log('SQL 更新语句:')
    console.log(`UPDATE admins SET password = '${hashedPassword}' WHERE username = 'admin';`)
    console.log('='.repeat(50))
    
    // 验证密码是否正确
    const isValid = await bcrypt.compare(password, hashedPassword)
    console.log(`密码验证: ${isValid ? '✅ 成功' : '❌ 失败'}`)
    
  } catch (error) {
    console.error('生成密码时出错:', error.message)
    process.exit(1)
  }
}

// 获取命令行参数
const password = process.argv[2]

if (password) {
  console.log(`使用自定义密码: ${password}`)
} else {
  console.log('使用默认密码: admin123')
}

generatePassword(password)
