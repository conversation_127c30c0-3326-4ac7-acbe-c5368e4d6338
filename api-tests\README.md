# API 接口测试文件

本目录包含了双重冥想Koa2项目的所有API接口测试文件，按模块分类组织。

## 目录结构

```
api-tests/
├── README.md                    # 本说明文件
├── common/                      # 公共配置和变量
│   └── variables.http          # 全局变量配置
├── auth/                       # 认证相关接口
│   ├── admin-auth.http         # 管理员认证
│   └── user-auth.http          # 用户认证
├── admin/                      # 管理后台接口
│   ├── admin-management.http   # 管理员管理
│   ├── user-management.http    # 用户管理
│   ├── user-level.http         # 用户等级管理
│   ├── meditation-content.http # 冥想内容管理
│   ├── meditation-tags.http    # 冥想标签管理
│   ├── plant-management.http   # 多肉管理
│   ├── plant-level.http        # 多肉等级管理
│   └── growth-records.http     # 成长记录管理
├── user/                       # 用户端接口
│   ├── profile.http            # 用户资料
│   ├── favorites.http          # 收藏功能
│   ├── plants.http             # 用户多肉
│   └── meditation-stats.http   # 冥想统计
├── meditation/                 # 冥想相关接口
│   ├── content.http            # 冥想内容
│   ├── search.http             # 搜索功能
│   └── tags.http               # 标签功能
├── plan/                       # 计划相关接口
│   ├── daily-plans.http        # 每日计划
│   ├── plan-management.http    # 计划管理
│   └── plan-stats.http         # 计划统计
└── plant/                      # 多肉相关接口
    ├── plant-list.http         # 多肉列表
    ├── plant-detail.http       # 多肉详情
    ├── energy-management.http  # 能量管理
    └── growth-records.http     # 成长记录
```

## 使用说明

1. **全局变量**: 所有测试文件都引用 `common/variables.http` 中定义的全局变量
2. **认证**: 大部分接口需要先通过认证获取token
3. **测试顺序**: 建议按照以下顺序进行测试：
   - 先运行认证相关测试获取token
   - 再运行具体功能模块的测试
4. **环境配置**: 可以通过修改 `common/variables.http` 来切换不同的测试环境

## 注意事项

- 测试前请确保服务器已启动
- 部分测试可能会修改数据库数据，请在测试环境中运行
- 管理员相关测试需要超级管理员权限
- 用户相关测试需要有效的微信用户token
