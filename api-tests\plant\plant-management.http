### 多肉管理接口测试
### 引用全局变量
< ../common/variables.http

### 前置条件：需要先获取用户token
< ../auth/user-auth.http

### 1. 获取多肉列表 - 默认获取
GET {{apiUrl}}/plant/list
Authorization: Bearer {{testUserToken}}

### 2. 获取多肉列表 - 包含详细信息
GET {{apiUrl}}/plant/list?include_details=true
Authorization: Bearer {{testUserToken}}

### 3. 获取多肉列表 - 按等级筛选
GET {{apiUrl}}/plant/list?level=2
Authorization: Bearer {{testUserToken}}

### 4. 获取多肉列表 - 按健康状态筛选
GET {{apiUrl}}/plant/list?health_status=healthy
Authorization: Bearer {{testUserToken}}

### 5. 获取多肉列表 - 按种类筛选
GET {{apiUrl}}/plant/list?species_id=1
Authorization: Bearer {{testUserToken}}

### 6. 获取多肉列表 - 按能量排序
GET {{apiUrl}}/plant/list?sort=energy&order=desc
Authorization: Bearer {{testUserToken}}

### 7. 获取多肉列表 - 按等级排序
GET {{apiUrl}}/plant/list?sort=level&order=desc
Authorization: Bearer {{testUserToken}}

### 8. 获取多肉列表 - 按创建时间排序
GET {{apiUrl}}/plant/list?sort=created_at&order=desc
Authorization: Bearer {{testUserToken}}

### 9. 获取多肉详情 - 正常多肉
GET {{apiUrl}}/plant/{{testPlantId}}
Authorization: Bearer {{testUserToken}}

### 10. 获取多肉详情 - 不存在的多肉
GET {{apiUrl}}/plant/99999
Authorization: Bearer {{testUserToken}}

### 11. 获取多肉详情 - 包含成长记录
GET {{apiUrl}}/plant/{{testPlantId}}?include_growth_records=true
Authorization: Bearer {{testUserToken}}

### 12. 获取多肉详情 - 包含统计信息
GET {{apiUrl}}/plant/{{testPlantId}}?include_stats=true
Authorization: Bearer {{testUserToken}}

### 13. 获取多肉详情 - 包含等级信息
GET {{apiUrl}}/plant/{{testPlantId}}?include_level_info=true
Authorization: Bearer {{testUserToken}}

### 14. 添加能量 - 正常添加
POST {{apiUrl}}/plant/{{testPlantId}}/energy
Authorization: Bearer {{testUserToken}}
Content-Type: application/json

{
  "energy_amount": 50,
  "source": "meditation_completed",
  "meditation_id": {{testMeditationId}},
  "notes": "完成冥想获得能量"
}

### 15. 添加能量 - 最小信息
POST {{apiUrl}}/plant/{{testPlantId}}/energy
Authorization: Bearer {{testUserToken}}
Content-Type: application/json

{
  "energy_amount": 30,
  "source": "daily_bonus"
}

### 16. 添加能量 - 大量能量
POST {{apiUrl}}/plant/{{testPlantId}}/energy
Authorization: Bearer {{testUserToken}}
Content-Type: application/json

{
  "energy_amount": 500,
  "source": "special_event",
  "notes": "特殊活动奖励"
}

### 17. 添加能量 - 零能量
POST {{apiUrl}}/plant/{{testPlantId}}/energy
Authorization: Bearer {{testUserToken}}
Content-Type: application/json

{
  "energy_amount": 0,
  "source": "test"
}

### 18. 添加能量 - 负数能量
POST {{apiUrl}}/plant/{{testPlantId}}/energy
Authorization: Bearer {{testUserToken}}
Content-Type: application/json

{
  "energy_amount": -10,
  "source": "penalty"
}

### 19. 添加能量 - 缺少必填字段
POST {{apiUrl}}/plant/{{testPlantId}}/energy
Authorization: Bearer {{testUserToken}}
Content-Type: application/json

{
  "source": "test"
}

### 20. 添加能量 - 无效来源
POST {{apiUrl}}/plant/{{testPlantId}}/energy
Authorization: Bearer {{testUserToken}}
Content-Type: application/json

{
  "energy_amount": 50,
  "source": "invalid_source"
}

### 21. 添加能量 - 不存在的多肉
POST {{apiUrl}}/plant/99999/energy
Authorization: Bearer {{testUserToken}}
Content-Type: application/json

{
  "energy_amount": 50,
  "source": "test"
}

### 22. 创建新多肉 - 正常创建
POST {{apiUrl}}/plant/create
Authorization: Bearer {{testUserToken}}
Content-Type: application/json

{
  "species_id": 1,
  "name": "我的小多肉",
  "description": "第一个多肉植物",
  "initial_energy": 100
}

### 23. 创建新多肉 - 最小信息
POST {{apiUrl}}/plant/create
Authorization: Bearer {{testUserToken}}
Content-Type: application/json

{
  "species_id": 1
}

### 24. 创建新多肉 - 自定义名称
POST {{apiUrl}}/plant/create
Authorization: Bearer {{testUserToken}}
Content-Type: application/json

{
  "species_id": 2,
  "name": "特殊多肉",
  "description": "这是一个特殊的多肉",
  "initial_energy": 200
}

### 25. 创建新多肉 - 缺少种类ID
POST {{apiUrl}}/plant/create
Authorization: Bearer {{testUserToken}}
Content-Type: application/json

{
  "name": "无种类多肉"
}

### 26. 创建新多肉 - 无效种类ID
POST {{apiUrl}}/plant/create
Authorization: Bearer {{testUserToken}}
Content-Type: application/json

{
  "species_id": 99999,
  "name": "无效种类多肉"
}

### 27. 创建新多肉 - 负数初始能量
POST {{apiUrl}}/plant/create
Authorization: Bearer {{testUserToken}}
Content-Type: application/json

{
  "species_id": 1,
  "initial_energy": -50
}

### 28. 获取成长记录 - 默认获取
GET {{apiUrl}}/plant/{{testPlantId}}/records
Authorization: Bearer {{testUserToken}}

### 29. 获取成长记录 - 指定分页
GET {{apiUrl}}/plant/{{testPlantId}}/records?page={{defaultPage}}&limit={{defaultLimit}}
Authorization: Bearer {{testUserToken}}

### 30. 获取成长记录 - 按类型筛选
GET {{apiUrl}}/plant/{{testPlantId}}/records?type=level_up&page=1&limit=10
Authorization: Bearer {{testUserToken}}

### 31. 获取成长记录 - 按时间范围筛选
GET {{apiUrl}}/plant/{{testPlantId}}/records?start_date=2024-01-01&end_date=2024-01-31&page=1&limit=20
Authorization: Bearer {{testUserToken}}

### 32. 获取成长记录 - 按时间排序
GET {{apiUrl}}/plant/{{testPlantId}}/records?sort=created_at&order=desc&page=1&limit=20
Authorization: Bearer {{testUserToken}}

### 33. 获取成长记录 - 不存在的多肉
GET {{apiUrl}}/plant/99999/records
Authorization: Bearer {{testUserToken}}

### 34. 权限测试 - 无token访问多肉列表
GET {{apiUrl}}/plant/list

### 35. 权限测试 - 无token访问多肉详情
GET {{apiUrl}}/plant/{{testPlantId}}

### 36. 权限测试 - 无token添加能量
POST {{apiUrl}}/plant/{{testPlantId}}/energy
Content-Type: application/json

{
  "energy_amount": 50,
  "source": "test"
}

### 37. 权限测试 - 无token创建多肉
POST {{apiUrl}}/plant/create
Content-Type: application/json

{
  "species_id": 1
}

### 38. 权限测试 - 无效token访问
GET {{apiUrl}}/plant/list
Authorization: Bearer invalid_token

### 39. 权限测试 - 访问他人多肉
# 注意：需要其他用户的多肉ID
GET {{apiUrl}}/plant/other_user_plant_id
Authorization: Bearer {{testUserToken}}

### 40. 批量操作测试 - 连续添加能量
POST {{apiUrl}}/plant/{{testPlantId}}/energy
Authorization: Bearer {{testUserToken}}
Content-Type: application/json

{
  "energy_amount": 25,
  "source": "batch_test_1"
}

###
POST {{apiUrl}}/plant/{{testPlantId}}/energy
Authorization: Bearer {{testUserToken}}
Content-Type: application/json

{
  "energy_amount": 25,
  "source": "batch_test_2"
}

### 41. 批量操作测试 - 连续创建多肉
POST {{apiUrl}}/plant/create
Authorization: Bearer {{testUserToken}}
Content-Type: application/json

{
  "species_id": 1,
  "name": "批量多肉1"
}

###
POST {{apiUrl}}/plant/create
Authorization: Bearer {{testUserToken}}
Content-Type: application/json

{
  "species_id": 2,
  "name": "批量多肉2"
}

### 42. 性能测试 - 大量成长记录查询
GET {{apiUrl}}/plant/{{testPlantId}}/records?page=1&limit=100
Authorization: Bearer {{testUserToken}}

### 43. 边界测试 - 超大能量值
POST {{apiUrl}}/plant/{{testPlantId}}/energy
Authorization: Bearer {{testUserToken}}
Content-Type: application/json

{
  "energy_amount": 999999,
  "source": "boundary_test"
}

### 44. 边界测试 - 超长名称
POST {{apiUrl}}/plant/create
Authorization: Bearer {{testUserToken}}
Content-Type: application/json

{
  "species_id": 1,
  "name": "超长名称测试".repeat(20),
  "description": "测试超长名称"
}

### 45. 数据一致性测试 - 检查能量变化
GET {{apiUrl}}/plant/{{testPlantId}}
Authorization: Bearer {{testUserToken}}

###
POST {{apiUrl}}/plant/{{testPlantId}}/energy
Authorization: Bearer {{testUserToken}}
Content-Type: application/json

{
  "energy_amount": 100,
  "source": "consistency_test"
}

###
GET {{apiUrl}}/plant/{{testPlantId}}
Authorization: Bearer {{testUserToken}}
