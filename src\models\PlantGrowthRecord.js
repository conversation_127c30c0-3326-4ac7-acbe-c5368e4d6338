import Sequelize from 'sequelize'
import sequelize from '../lib/sequelize.js'

const PlantGrowthRecord = sequelize.define('plant_growth_records', {
  id: {
    type: Sequelize.BIGINT,
    primaryKey: true,
    autoIncrement: true
  },
  plant_id: {
    type: Sequelize.BIGINT,
    allowNull: false
  },
  change_value: {
    type: Sequelize.INTEGER,
    allowNull: false,
    comment: '能量值变化'
  },
  reason: {
    type: Sequelize.STRING(255),
    allowNull: true
  },
  created_at: {
    type: Sequelize.DATE,
    defaultValue: Sequelize.NOW
  }
}, {
  timestamps: false,
  tableName: 'plant_growth_records'
})

export default PlantGrowthRecord