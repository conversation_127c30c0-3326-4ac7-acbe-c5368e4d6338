### 用户等级管理接口测试
### 引用全局变量
< ../common/variables.http

### 前置条件：需要先获取管理员token
< ../auth/admin-auth.http

### 1. 获取用户等级配置列表 - 默认分页
GET {{baseUrl}}/admin/user-levels?page={{defaultPage}}&limit={{defaultLimit}}
Authorization: Bearer {{adminToken}}

### 2. 获取用户等级配置列表 - 搜索功能
GET {{baseUrl}}/admin/user-levels?search=新手&page=1&limit=10
Authorization: Bearer {{adminToken}}

### 3. 获取用户等级配置列表 - 按状态筛选
GET {{baseUrl}}/admin/user-levels?is_active=true&page=1&limit=10
Authorization: Bearer {{adminToken}}

### 4. 获取用户等级配置列表 - 按等级排序
GET {{baseUrl}}/admin/user-levels?sort=level&order=asc&page=1&limit=10
Authorization: Bearer {{adminToken}}

### 5. 获取用户等级配置列表 - 按经验需求排序
GET {{baseUrl}}/admin/user-levels?sort=required_experience&order=desc&page=1&limit=10
Authorization: Bearer {{adminToken}}

### 6. 获取指定等级配置详情 - 正常等级
GET {{baseUrl}}/admin/user-levels/{{testLevelId}}
Authorization: Bearer {{adminToken}}

### 7. 获取指定等级配置详情 - 不存在的等级
GET {{baseUrl}}/admin/user-levels/99999
Authorization: Bearer {{adminToken}}

### 8. 创建用户等级配置 - 完整配置
POST {{baseUrl}}/admin/user-levels
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "level": 6,
  "name": "冥想大师",
  "icon": "/images/levels/master.png",
  "required_experience": 5000,
  "required_days": 100,
  "privileges": {
    "max_daily_plans": 10,
    "can_create_custom_plans": true,
    "can_access_premium_content": true,
    "energy_bonus_multiplier": 2.0,
    "special_features": ["advanced_analytics", "priority_support"]
  },
  "rewards": {
    "coins": 1000,
    "energy": 500,
    "items": ["premium_decoration", "master_badge"],
    "special_title": "冥想大师"
  },
  "description": "达到冥想大师级别，享受全部高级功能",
  "color": "#FFD700",
  "sort_order": 6
}

### 9. 创建用户等级配置 - 最小配置
POST {{baseUrl}}/admin/user-levels
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "level": 7,
  "name": "测试等级",
  "required_experience": 1000,
  "description": "测试用等级配置"
}

### 10. 创建用户等级配置 - 缺少必填字段
POST {{baseUrl}}/admin/user-levels
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "name": "缺少等级字段",
  "required_experience": 500
}

### 11. 创建用户等级配置 - 重复等级
POST {{baseUrl}}/admin/user-levels
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "level": 1,
  "name": "重复等级测试",
  "required_experience": 100,
  "description": "测试重复等级"
}

### 12. 创建用户等级配置 - 负数等级
POST {{baseUrl}}/admin/user-levels
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "level": -1,
  "name": "负数等级",
  "required_experience": 100,
  "description": "测试负数等级"
}

### 13. 创建用户等级配置 - 零经验需求
POST {{baseUrl}}/admin/user-levels
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "level": 8,
  "name": "零经验等级",
  "required_experience": 0,
  "description": "测试零经验需求"
}

### 14. 更新用户等级配置 - 正常更新
PUT {{baseUrl}}/admin/user-levels/{{testLevelId}}
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "name": "新手（更新）",
  "description": "刚开始冥想之旅的用户（已更新）",
  "required_experience": 120
}

### 15. 更新用户等级配置 - 更新权限
PUT {{baseUrl}}/admin/user-levels/{{testLevelId}}
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "privileges": {
    "max_daily_plans": 5,
    "can_create_custom_plans": false,
    "can_access_premium_content": false,
    "energy_bonus_multiplier": 1.2
  }
}

### 16. 更新用户等级配置 - 更新奖励
PUT {{baseUrl}}/admin/user-levels/{{testLevelId}}
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "rewards": {
    "coins": 100,
    "energy": 50,
    "items": ["basic_decoration", "newbie_badge"],
    "special_title": "冥想新手"
  }
}

### 17. 更新用户等级配置 - 更新外观
PUT {{baseUrl}}/admin/user-levels/{{testLevelId}}
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "icon": "/images/levels/newbie_updated.png",
  "color": "#87CEEB"
}

### 18. 更新用户等级配置 - 不存在的等级
PUT {{baseUrl}}/admin/user-levels/99999
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "name": "不存在的等级"
}

### 19. 删除用户等级配置 - 正常删除
DELETE {{baseUrl}}/admin/user-levels/6
Authorization: Bearer {{adminToken}}

### 20. 删除用户等级配置 - 不存在的等级
DELETE {{baseUrl}}/admin/user-levels/99999
Authorization: Bearer {{adminToken}}

### 21. 删除用户等级配置 - 尝试删除正在使用的等级
DELETE {{baseUrl}}/admin/user-levels/1
Authorization: Bearer {{adminToken}}

### 22. 获取等级统计信息 - 基础统计
GET {{baseUrl}}/admin/user-levels/statistics
Authorization: Bearer {{adminToken}}

### 23. 获取等级统计信息 - 用户分布统计
GET {{baseUrl}}/admin/user-levels/statistics?include_user_distribution=true
Authorization: Bearer {{adminToken}}

### 24. 获取等级统计信息 - 升级趋势统计
GET {{baseUrl}}/admin/user-levels/statistics?include_upgrade_trends=true&days=30
Authorization: Bearer {{adminToken}}

### 25. 批量操作 - 批量更新排序
PUT {{baseUrl}}/admin/user-levels/batch/sort
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "levels": [
    {"id": 1, "sort_order": 1},
    {"id": 2, "sort_order": 2},
    {"id": 3, "sort_order": 3}
  ]
}

### 26. 批量操作 - 批量启用/禁用
PUT {{baseUrl}}/admin/user-levels/batch/toggle
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "ids": [1, 2, 3],
  "is_active": true
}

### 27. 等级预览 - 预览等级效果
GET {{baseUrl}}/admin/user-levels/{{testLevelId}}/preview
Authorization: Bearer {{adminToken}}

### 28. 等级验证 - 验证等级配置
POST {{baseUrl}}/admin/user-levels/validate
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "level": 9,
  "name": "验证测试等级",
  "required_experience": 2000,
  "privileges": {
    "max_daily_plans": 8
  }
}

### 29. 等级模板 - 获取等级模板
GET {{baseUrl}}/admin/user-levels/templates
Authorization: Bearer {{adminToken}}

### 30. 等级模板 - 应用模板
POST {{baseUrl}}/admin/user-levels/apply-template
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "template_id": "standard_progression",
  "start_level": 10,
  "level_count": 5
}

### 31. 权限测试 - 无token访问
GET {{baseUrl}}/admin/user-levels

### 32. 权限测试 - 无效token访问
GET {{baseUrl}}/admin/user-levels
Authorization: Bearer invalid_token

### 33. 权限测试 - 普通管理员权限
# 注意：需要普通管理员token
GET {{baseUrl}}/admin/user-levels
Authorization: Bearer normal_admin_token_here

### 34. 性能测试 - 大量数据查询
GET {{baseUrl}}/admin/user-levels?page=1&limit=100
Authorization: Bearer {{adminToken}}

### 35. 数据验证测试 - 超长名称
POST {{baseUrl}}/admin/user-levels
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "level": 10,
  "name": "超长名称测试".repeat(20),
  "required_experience": 1000,
  "description": "测试超长名称"
}

### 36. 数据验证测试 - 特殊字符
POST {{baseUrl}}/admin/user-levels
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "level": 11,
  "name": "特殊字符@#$%^&*()",
  "required_experience": 1000,
  "description": "测试特殊字符"
}

### 37. 数据验证测试 - 超大经验值
POST {{baseUrl}}/admin/user-levels
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "level": 12,
  "name": "超大经验值测试",
  "required_experience": 999999999,
  "description": "测试超大经验值"
}

### 38. 等级关系测试 - 检查等级递进关系
GET {{baseUrl}}/admin/user-levels/relationship-check
Authorization: Bearer {{adminToken}}

### 39. 用户升级模拟 - 模拟用户升级
POST {{baseUrl}}/admin/user-levels/simulate-upgrade
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "user_id": {{testUserId}},
  "target_level": 3,
  "dry_run": true
}

### 40. 等级影响分析 - 分析等级变更影响
POST {{baseUrl}}/admin/user-levels/impact-analysis
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "level_id": {{testLevelId}},
  "changes": {
    "required_experience": 200,
    "privileges": {
      "max_daily_plans": 6
    }
  }
}
