import { WeChat as WeChatConfig } from '../config.js'
import axios from 'axios'

/**
 * 微信小程序服务类
 */
export default class WeChatService {
  /**
   * 通过微信code换取openid和session_key
   * @param {string} code - 微信小程序登录时获取的code
   * @returns {Promise<Object>} 包含openid、session_key等信息的对象
   */
  static async code2Session(code) {
    try {
      const response = await axios.get(WeChatConfig.code2session_url, {
        params: {
          appid: WeChatConfig.appid,
          secret: WeChatConfig.secret,
          js_code: code,
          grant_type: 'authorization_code'
        }
      })

      const data = response.data

      // 检查是否有错误
      if (data.errcode) {
        throw new Error(`微信API错误: ${data.errcode} - ${data.errmsg}`)
      }

      return {
        openid: data.openid,
        session_key: data.session_key,
        unionid: data.unionid // 可能为空
      }
    } catch (error) {
      console.error('微信code2session调用失败:', error)
      throw error
    }
  }

  /**
   * 解密微信小程序数据
   * @param {string} encryptedData - 加密数据
   * @param {string} iv - 初始向量
   * @param {string} sessionKey - 会话密钥
   * @returns {Object} 解密后的数据
   */
  static decryptData(encryptedData, iv, sessionKey) {
    // 这里可以实现微信数据解密逻辑
    // 需要安装crypto模块并实现AES解密
    // 暂时返回空对象，实际使用时需要完善
    return {}
  }
}
