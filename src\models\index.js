import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

// 在ES模块中获取__dirname的等价物
const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// 手动实现requireDirectory的功能
const models = {}

// 读取当前目录下的所有.js文件（除了index.js）
const files = fs.readdirSync(__dirname)
  .filter(file => file.endsWith('.js') && file !== 'index.js')

// 动态导入所有模型文件
for (const file of files) {
  const moduleName = path.basename(file, '.js')
  try {
    const module = await import(`./${file}`)
    models[moduleName] = module.default || module
  } catch (error) {
    console.error(`Error importing model ${file}:`, error)
  }
}

export default models
